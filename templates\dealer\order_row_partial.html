<tr class="hover:bg-gray-50" id="order-row-{{ order.id }}">
    <td class="px-6 py-4 whitespace-nowrap">
        <input type="checkbox" name="order_checkbox" value="{{ order.id }}"
               @change="toggleOrderSelection({{ order.id }})"
               :checked="selectedOrders.includes({{ order.id }})"
               class="focus:ring-prycegas-orange h-4 w-4 text-prycegas-orange border-gray-300 rounded">
    </td>
    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
        #{{ order.id }}
    </td>
    <td class="px-6 py-4 whitespace-nowrap">
        <div class="text-sm text-gray-900">{{ order.customer.username }}</div>
        {% if order.customer.first_name or order.customer.last_name %}
            <div class="text-sm text-gray-500">{{ order.customer.first_name }} {{ order.customer.last_name }}</div>
        {% endif %}
    </td>
    <td class="px-6 py-4 whitespace-nowrap">
        <div class="text-sm text-gray-900">{{ order.product.name }}</div>
        <div class="text-sm text-gray-500">{{ order.product.size }}</div>
    </td>
    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {{ order.quantity }}
    </td>
    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        ₱{{ order.total_amount|floatformat:2 }}
    </td>
    <td class="px-6 py-4 whitespace-nowrap">
        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                     {% if order.delivery_type == 'delivery' %}bg-blue-100 text-blue-800{% else %}bg-gray-100 text-gray-800{% endif %}">
            {{ order.get_delivery_type_display }}
        </span>
    </td>
    <td class="px-6 py-4 whitespace-nowrap">
        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                     {% if order.status == 'pending' %}bg-yellow-100 text-yellow-800
                     {% elif order.status == 'out_for_delivery' %}bg-orange-100 text-orange-800
                     {% elif order.status == 'delivered' %}bg-green-100 text-green-800
                     {% elif order.status == 'cancelled' %}bg-red-100 text-red-800
                     {% endif %}">
            {{ order.get_status_display }}
        </span>
    </td>
    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
        {{ order.order_date|date:"M d, Y" }}
        <div class="text-xs text-gray-400">{{ order.order_date|time:"g:i A" }}</div>
    </td>
    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
        <div class="flex items-center space-x-2">
            <!-- View Details Button -->
            <button @click="showOrderDetail({{ order.id }})"
                    class="text-prycegas-orange hover:text-prycegas-orange-dark">
                <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                </svg>
            </button>

            <!-- Status Update Dropdown -->
            {% if order.status != 'delivered' and order.status != 'cancelled' %}
                <div class="relative" x-data="{ open: false }">
                    <button @click="open = !open" 
                            class="text-gray-400 hover:text-gray-600">
                        <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"/>
                        </svg>
                    </button>
                    <div x-show="open" @click.away="open = false" x-transition
                         class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-200">
                        <div class="py-1">
                            {% if order.status == 'pending' %}
                                <button hx-post="{% url 'core:update_order_status' order.id %}"
                                        hx-vals='{"status": "out_for_delivery"}'
                                        hx-target="#order-row-{{ order.id }}"
                                        hx-swap="outerHTML"
                                        @click="open = false"
                                        class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    Mark as Out for Delivery
                                </button>
                                <button hx-post="{% url 'core:update_order_status' order.id %}"
                                        hx-vals='{"status": "cancelled"}'
                                        hx-target="#order-row-{{ order.id }}"
                                        hx-swap="outerHTML"
                                        @click="open = false"
                                        class="block w-full text-left px-4 py-2 text-sm text-red-700 hover:bg-red-50">
                                    Cancel Order
                                </button>
                            {% elif order.status == 'out_for_delivery' %}
                                <button hx-post="{% url 'core:update_order_status' order.id %}"
                                        hx-vals='{"status": "delivered"}'
                                        hx-target="#order-row-{{ order.id }}"
                                        hx-swap="outerHTML"
                                        @click="open = false"
                                        class="block w-full text-left px-4 py-2 text-sm text-green-700 hover:bg-green-50">
                                    Mark as Delivered
                                </button>
                                <button hx-post="{% url 'core:update_order_status' order.id %}"
                                        hx-vals='{"status": "cancelled"}'
                                        hx-target="#order-row-{{ order.id }}"
                                        hx-swap="outerHTML"
                                        @click="open = false"
                                        class="block w-full text-left px-4 py-2 text-sm text-red-700 hover:bg-red-50">
                                    Cancel Order
                                </button>
                            {% endif %}
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>
    </td>
</tr>

{% if show_success_message %}
    <script>
        // Show success toast for status update
        setTimeout(() => {
            const toast = document.createElement('div');
            toast.className = 'fixed top-4 right-4 px-6 py-3 rounded-md text-white z-50 bg-green-500';
            toast.textContent = '{{ success_message }}';
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.remove();
            }, 3000);
        }, 100);
    </script>
{% endif %}