/* Custom styles for Prycegas Station */

/* Loading spinner animation */
@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.animate-spin {
    animation: spin 1s linear infinite;
}

/* Custom button styles */
.btn-primary {
    @apply bg-prycegas-orange hover:bg-prycegas-orange-dark text-white font-medium py-2 px-4 rounded transition-colors duration-150;
}

.btn-secondary {
    @apply bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded transition-colors duration-150;
}

.btn-success {
    @apply bg-green-500 hover:bg-green-600 text-white font-medium py-2 px-4 rounded transition-colors duration-150;
}

.btn-danger {
    @apply bg-red-500 hover:bg-red-600 text-white font-medium py-2 px-4 rounded transition-colors duration-150;
}

.btn-warning {
    @apply bg-yellow-500 hover:bg-yellow-600 text-white font-medium py-2 px-4 rounded transition-colors duration-150;
}

.btn-info {
    @apply bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded transition-colors duration-150;
}

/* Form styles */
.form-input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-prycegas-orange focus:border-prycegas-orange;
}

.form-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
}

.form-error {
    @apply text-red-600 text-sm mt-1;
}

.form-help {
    @apply text-gray-500 text-sm mt-1;
}

/* Card styles */
.card {
    @apply bg-white shadow rounded-lg overflow-hidden;
}

.card-header {
    @apply px-6 py-4 border-b border-gray-200 bg-gray-50;
}

.card-body {
    @apply px-6 py-4;
}

.card-footer {
    @apply px-6 py-4 border-t border-gray-200 bg-gray-50;
}

/* Table styles */
.table {
    @apply min-w-full divide-y divide-gray-200;
}

.table th {
    @apply px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
}

.table td {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
}

.table tbody tr:nth-child(even) {
    @apply bg-gray-50;
}

.table tbody tr:hover {
    @apply bg-gray-100;
}

/* Status badges */
.badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.badge-pending {
    @apply bg-yellow-100 text-yellow-800;
}

.badge-processing {
    @apply bg-blue-100 text-blue-800;
}

.badge-delivered {
    @apply bg-green-100 text-green-800;
}

.badge-cancelled {
    @apply bg-red-100 text-red-800;
}

/* Mobile responsive utilities */
@media (max-width: 640px) {
    .mobile-hide {
        display: none;
    }
    
    .mobile-full {
        width: 100%;
    }
    
    .mobile-stack {
        flex-direction: column;
    }
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    .print-break {
        page-break-after: always;
    }
    
    body {
        font-size: 12pt;
        line-height: 1.4;
    }
    
    .sidebar {
        display: none !important;
    }
    
    main {
        margin-left: 0 !important;
    }
}

/* Accessibility improvements */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus styles for better accessibility */
.focus-ring:focus {
    @apply outline-none ring-2 ring-prycegas-orange ring-offset-2;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .bg-prycegas-orange {
        background-color: #ff4500 !important;
    }
    
    .text-prycegas-orange {
        color: #ff4500 !important;
    }
    
    .border-prycegas-orange {
        border-color: #ff4500 !important;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}