{% extends 'base.html' %}

{% block title %}Inventory Management - Prycegas Station{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50" x-data="inventoryManager()">
    <!-- Header -->
    <div class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Inventory Management</h1>
                    <p class="mt-1 text-sm text-gray-600">Manage stock levels and track deliveries</p>
                </div>
                <button 
                    @click="openDeliveryModal()"
                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-prycegas-orange hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-prycegas-orange transition-colors duration-200">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Log Delivery
                </button>
            </div>
        </div>
    </div>

    <!-- Inventory Statistics -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div id="inventory-stats" up-target="inventory-stats">
            {% include 'dealer/inventory_dashboard_partial.html' %}
        </div>

        <!-- Main Content Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mt-6">
            <!-- Current Stock Levels -->
            <div class="lg:col-span-2">
                <div class="bg-white shadow rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <h2 class="text-lg font-medium text-gray-900">Current Stock Levels</h2>
                            <button 
                                up-target="#inventory-stats"
                                up-href="{% url 'core:refresh_inventory_dashboard' %}"
                                class="text-sm text-prycegas-orange hover:text-orange-600 font-medium">
                                <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                </svg>
                                Refresh
                            </button>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            {% for product in products %}
                            <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg {% if product.is_low_stock %}bg-red-50 border-red-200{% endif %}">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <div class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                                            <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                            </svg>
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <h3 class="text-sm font-medium text-gray-900">{{ product.name }} - {{ product.size }}</h3>
                                        <p class="text-sm text-gray-500">₱{{ product.price|floatformat:2 }} per unit</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <div class="flex items-center">
                                        {% if product.is_low_stock %}
                                        <svg class="w-5 h-5 text-red-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                        </svg>
                                        {% endif %}
                                        <span class="text-lg font-semibold {% if product.is_low_stock %}text-red-600{% else %}text-gray-900{% endif %}">
                                            {{ product.current_stock }}
                                        </span>
                                        <span class="text-sm text-gray-500 ml-1">units</span>
                                    </div>
                                    {% if product.is_low_stock %}
                                    <p class="text-xs text-red-600 mt-1">Low Stock Alert</p>
                                    {% endif %}
                                    <p class="text-xs text-gray-500 mt-1">Min: {{ product.minimum_stock }}</p>
                                </div>
                            </div>
                            {% empty %}
                            <div class="text-center py-8">
                                <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                </svg>
                                <p class="text-gray-500">No products found</p>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Stock Movement History -->
            <div class="lg:col-span-1">
                <div class="bg-white shadow rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <h2 class="text-lg font-medium text-gray-900">Recent Deliveries</h2>
                            <button 
                                hx-get="{% url 'core:refresh_stock_movements' %}"
                                hx-target="#stock-movements"
                                class="text-sm text-prycegas-orange hover:text-orange-600 font-medium">
                                <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                </svg>
                                Refresh
                            </button>
                        </div>
                    </div>
                    <div id="stock-movements" class="p-6">
                        {% include 'dealer/stock_movements_partial.html' %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Delivery Logging Modal -->
    <div x-show="showDeliveryModal" 
         x-transition:enter="ease-out duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="ease-in duration-200"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
         style="display: none;">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div id="delivery-modal-content">
                <!-- Modal content will be loaded here via HTMX -->
            </div>
        </div>
    </div>
</div>

<script>
function inventoryManager() {
    return {
        showDeliveryModal: false,
        selectedProduct: '',
        quantity: 1,
        costPerUnit: 0,
        totalCost: 0,

        openDeliveryModal() {
            this.showDeliveryModal = true;
            // Load delivery form via HTMX
            htmx.ajax('GET', '{% url "core:get_delivery_form" %}', {
                target: '#delivery-modal-content'
            });
        },

        closeDeliveryModal() {
            this.showDeliveryModal = false;
            this.resetForm();
        },

        calculateTotal() {
            if (this.quantity && this.costPerUnit) {
                this.totalCost = (this.quantity * this.costPerUnit).toFixed(2);
            }
        },

        resetForm() {
            this.selectedProduct = '';
            this.quantity = 1;
            this.costPerUnit = 0;
            this.totalCost = 0;
        },

        submitDelivery(formData) {
            // Handle form submission via HTMX
            htmx.ajax('POST', '{% url "core:log_delivery" %}', {
                values: formData,
                target: '#delivery-modal-content'
            }).then(() => {
                // Refresh inventory after successful submission
                htmx.ajax('GET', '{% url "core:refresh_inventory_dashboard" %}', {
                    target: '#inventory-stats'
                });
                htmx.ajax('GET', '{% url "core:refresh_stock_movements" %}', {
                    target: '#stock-movements'
                });
            });
        }
    }
}
</script>
{% endblock %}