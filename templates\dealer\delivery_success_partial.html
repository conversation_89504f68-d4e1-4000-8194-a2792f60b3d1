<!-- Delivery Success Message -->
<div class="relative">
    <!-- Success Header -->
    <div class="flex items-center justify-between pb-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-green-600">Delivery Logged Successfully</h3>
        <button @click="$parent.closeDeliveryModal()" 
                class="text-gray-400 hover:text-gray-600 focus:outline-none">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
        </button>
    </div>

    <!-- Success Content -->
    <div class="py-6">
        <div class="flex items-center justify-center mb-6">
            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
            </div>
        </div>

        <div class="text-center">
            <p class="text-lg font-medium text-gray-900 mb-2">{{ message }}</p>
            
            <div class="bg-green-50 border border-green-200 rounded-lg p-4 mt-4">
                <div class="text-sm text-green-800">
                    <p><strong>Product:</strong> {{ delivery_log.product.name }} - {{ delivery_log.product.size }}</p>
                    <p><strong>Quantity:</strong> {{ delivery_log.quantity_received }} units</p>
                    <p><strong>Supplier:</strong> {{ delivery_log.supplier }}</p>
                    <p><strong>Total Cost:</strong> ₱{{ delivery_log.total_cost|floatformat:2 }}</p>
                    <p><strong>New Stock Level:</strong> {{ delivery_log.product.current_stock }} units</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Actions -->
    <div class="flex items-center justify-center space-x-3 pt-6 border-t border-gray-200">
        <button @click="$parent.closeDeliveryModal(); refreshInventory()"
                class="px-6 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-prycegas-orange hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-prycegas-orange">
            Close & Refresh
        </button>
    </div>
</div>

<script>
function refreshInventory() {
    // Refresh inventory dashboard
    htmx.ajax('GET', '{% url "core:refresh_inventory_dashboard" %}', {
        target: '#inventory-stats'
    });
    
    // Refresh stock movements
    htmx.ajax('GET', '{% url "core:refresh_stock_movements" %}', {
        target: '#stock-movements'
    });
}
</script>