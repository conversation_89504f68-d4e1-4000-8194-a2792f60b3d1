from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator
from decimal import Decimal


class CustomerProfile(models.Model):
    """
    Extended profile for customers with delivery information
    Requirements: 1.2 - Customer registration with contact information
    """
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='customer_profile')
    phone_number = models.CharField(max_length=15, help_text="Customer contact number")
    address = models.TextField(help_text="Customer delivery address")
    delivery_instructions = models.TextField(
        blank=True, 
        help_text="Special delivery instructions (optional)"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Customer Profile"
        verbose_name_plural = "Customer Profiles"

    def __str__(self):
        return f"{self.user.username} - {self.phone_number}"


class LPGProduct(models.Model):
    """
    LPG products with stock tracking and pricing
    Requirements: 2.3, 6.2 - Product management with inventory tracking
    """
    name = models.CharField(max_length=100, help_text="Product name (e.g., LPG Gas)")
    size = models.CharField(max_length=50, help_text="Product size (e.g., 11kg, 22kg)")
    price = models.DecimalField(
        max_digits=10, 
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))],
        help_text="Price per unit"
    )
    current_stock = models.IntegerField(
        default=0,
        validators=[MinValueValidator(0)],
        help_text="Current available stock"
    )
    minimum_stock = models.IntegerField(
        default=10,
        validators=[MinValueValidator(0)],
        help_text="Minimum stock level for low stock alerts"
    )
    is_active = models.BooleanField(default=True, help_text="Product availability status")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "LPG Product"
        verbose_name_plural = "LPG Products"
        ordering = ['name', 'size']

    def __str__(self):
        return f"{self.name} - {self.size}"

    @property
    def is_low_stock(self):
        """Check if product is below minimum stock level"""
        return self.current_stock <= self.minimum_stock

    def can_fulfill_order(self, quantity):
        """Check if there's enough stock for an order"""
        return self.current_stock >= quantity and self.is_active


class Order(models.Model):
    """
    Customer orders with status workflow and delivery options
    Requirements: 2.3, 5.5 - Order management with status tracking
    """
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('out_for_delivery', 'Out for Delivery'),
        ('delivered', 'Delivered'),
        ('cancelled', 'Cancelled'),
    ]
    
    DELIVERY_CHOICES = [
        ('pickup', 'Pickup'),
        ('delivery', 'Delivery'),
    ]
    
    customer = models.ForeignKey(
        User, 
        on_delete=models.CASCADE, 
        related_name='orders',
        help_text="Customer who placed the order"
    )
    product = models.ForeignKey(
        LPGProduct, 
        on_delete=models.CASCADE, 
        related_name='orders',
        help_text="Ordered product"
    )
    quantity = models.IntegerField(
        validators=[MinValueValidator(1)],
        help_text="Number of units ordered"
    )
    delivery_type = models.CharField(
        max_length=20, 
        choices=DELIVERY_CHOICES,
        help_text="Pickup or delivery option"
    )
    delivery_address = models.TextField(help_text="Address for delivery orders")
    status = models.CharField(
        max_length=20, 
        choices=STATUS_CHOICES, 
        default='pending',
        help_text="Current order status"
    )
    total_amount = models.DecimalField(
        max_digits=10, 
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))],
        help_text="Total order amount"
    )
    order_date = models.DateTimeField(auto_now_add=True)
    delivery_date = models.DateTimeField(
        null=True, 
        blank=True,
        help_text="Date when order was delivered"
    )
    notes = models.TextField(
        blank=True,
        help_text="Additional order notes or instructions"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Order"
        verbose_name_plural = "Orders"
        ordering = ['-order_date']

    def __str__(self):
        return f"Order #{self.id} - {self.customer.username} - {self.product.name}"

    def save(self, *args, **kwargs):
        """Calculate total amount on save"""
        if not self.total_amount:
            self.total_amount = self.product.price * self.quantity
        super().save(*args, **kwargs)

    @property
    def is_delivered(self):
        """Check if order has been delivered"""
        return self.status == 'delivered'

    @property
    def can_be_cancelled(self):
        """Check if order can still be cancelled"""
        return self.status in ['pending']


class DeliveryLog(models.Model):
    """
    Log of distributor deliveries for inventory management
    Requirements: 6.2 - Inventory management with delivery tracking
    """
    product = models.ForeignKey(
        LPGProduct, 
        on_delete=models.CASCADE, 
        related_name='delivery_logs',
        help_text="Product that was delivered"
    )
    quantity_received = models.IntegerField(
        validators=[MinValueValidator(1)],
        help_text="Number of units received"
    )
    supplier = models.CharField(
        max_length=100,
        help_text="Name of the supplier/distributor"
    )
    delivery_date = models.DateTimeField(help_text="Date when delivery was received")
    cost_per_unit = models.DecimalField(
        max_digits=10, 
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))],
        help_text="Cost per unit from supplier"
    )
    total_cost = models.DecimalField(
        max_digits=10, 
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))],
        help_text="Total cost of the delivery"
    )
    logged_by = models.ForeignKey(
        User, 
        on_delete=models.CASCADE, 
        related_name='delivery_logs',
        help_text="Staff member who logged this delivery"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    notes = models.TextField(
        blank=True,
        help_text="Additional notes about the delivery"
    )

    class Meta:
        verbose_name = "Delivery Log"
        verbose_name_plural = "Delivery Logs"
        ordering = ['-delivery_date']

    def __str__(self):
        return f"Delivery: {self.quantity_received}x {self.product.name} from {self.supplier}"

    def save(self, *args, **kwargs):
        """Calculate total cost and update product stock on save"""
        if not self.total_cost:
            self.total_cost = self.cost_per_unit * self.quantity_received
        
        # Update product stock when delivery is logged
        if self.pk is None:  # Only on creation, not updates
            self.product.current_stock += self.quantity_received
            self.product.save()
        
        super().save(*args, **kwargs)