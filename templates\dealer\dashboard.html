{% extends 'base.html' %}

{% block title %}Dealer Dashboard - Prycegas Station{% endblock %}

{% block content %}
<div class="dealer-dashboard" x-data="{ 
    autoRefresh: true,
    refreshInterval: null,
    startAutoRefresh() {
        if (this.autoRefresh) {
            this.refreshInterval = setInterval(() => {
                // Refresh dashboard stats using Unpoly
                up.reload('#dashboard-stats', { cache: false });
                up.reload('#recent-activity', { cache: false });
            }, 30000); // Refresh every 30 seconds
        }
    },
    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    },
    toggleAutoRefresh() {
        this.autoRefresh = !this.autoRefresh;
        if (this.autoRefresh) {
            this.startAutoRefresh();
        } else {
            this.stopAutoRefresh();
        }
    }
}" x-init="startAutoRefresh()" x-on:beforeunload.window="stopAutoRefresh()">

    <!-- Page Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Dealer Dashboard</h1>
                <p class="mt-2 text-gray-600">Overview of orders, inventory, and recent activity</p>
            </div>
            <div class="flex items-center space-x-4">
                <!-- Auto-refresh toggle -->
                <div class="flex items-center">
                    <label class="flex items-center cursor-pointer">
                        <input type="checkbox" x-model="autoRefresh" @change="toggleAutoRefresh()" 
                               class="sr-only">
                        <div class="relative">
                            <div class="block bg-gray-600 w-14 h-8 rounded-full"></div>
                            <div class="dot absolute left-1 top-1 bg-white w-6 h-6 rounded-full transition"
                                 :class="{ 'transform translate-x-6 bg-prycegas-orange': autoRefresh }"></div>
                        </div>
                        <span class="ml-3 text-sm text-gray-700">Auto-refresh</span>
                    </label>
                </div>
                <!-- Manual refresh button -->
                <button @click="up.reload('#dashboard-stats, #recent-activity', { cache: false })"
                        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-prycegas-orange hover:bg-prycegas-orange-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-prycegas-orange transition-colors duration-150">
                    <svg class="mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                    </svg>
                    Refresh
                </button>
            </div>
        </div>
    </div>

    <!-- Dashboard Statistics -->
    <div id="dashboard-stats" up-source="{% url 'core:refresh_dashboard_stats' %}">
        {% include 'dealer/dashboard_stats_partial.html' %}
    </div>

    <!-- Recent Activity Section -->
    <div id="recent-activity" up-source="{% url 'core:refresh_recent_activity' %}" class="mt-8">
        {% include 'dealer/recent_activity_partial.html' %}
    </div>

</div>

<style>
/* Custom toggle switch styling */
.dot {
    transition: transform 0.3s ease, background-color 0.3s ease;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// Toast notification for successful refreshes
document.addEventListener('up:fragment:loaded', function(event) {
    if (event.target.id === 'dashboard-stats' || event.target.id === 'recent-activity') {
        // Show subtle success indicator
        const indicator = document.createElement('div');
        indicator.className = 'fixed top-4 right-4 bg-green-500 text-white px-3 py-1 rounded text-sm z-50';
        indicator.textContent = 'Dashboard updated';
        document.body.appendChild(indicator);
        
        setTimeout(() => {
            indicator.remove();
        }, 2000);
    }
});
</script>
{% endblock %}