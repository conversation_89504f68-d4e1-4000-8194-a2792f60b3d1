{% extends 'base.html' %}

{% block title %}Profile - Prycegas Station{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto">
    <!-- Page header -->
    <div class="bg-white shadow rounded-lg mb-6">
        <div class="px-4 py-5 sm:p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="h-12 w-12 rounded-full bg-prycegas-orange flex items-center justify-center">
                        <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <h1 class="text-2xl font-bold text-gray-900">My Profile</h1>
                    <p class="text-sm text-gray-600">Manage your account information and delivery preferences</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Display messages -->
    {% if messages %}
        <div class="mb-6 space-y-2">
            {% for message in messages %}
                <div class="rounded-md p-4 {% if message.tags == 'error' %}bg-red-50 border border-red-200{% elif message.tags == 'success' %}bg-green-50 border border-green-200{% else %}bg-blue-50 border border-blue-200{% endif %}">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            {% if message.tags == 'error' %}
                                <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                                </svg>
                            {% elif message.tags == 'success' %}
                                <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                </svg>
                            {% else %}
                                <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                                </svg>
                            {% endif %}
                        </div>
                        <div class="ml-3">
                            <p class="text-sm {% if message.tags == 'error' %}text-red-800{% elif message.tags == 'success' %}text-green-800{% else %}text-blue-800{% endif %}">
                                {{ message }}
                            </p>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    {% endif %}

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Profile Information -->
        <div class="lg:col-span-2">
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h2 class="text-lg font-medium text-gray-900 mb-4">Profile Information</h2>
                    
                    <form method="post" x-data="profileForm()" @submit="handleSubmit">
                        {% csrf_token %}
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Username (read-only) -->
                            <div class="md:col-span-2">
                                <label class="block text-sm font-medium text-gray-700">Username</label>
                                <div class="mt-1 px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-gray-500">
                                    {{ user.username }}
                                </div>
                                <p class="mt-1 text-xs text-gray-500">Username cannot be changed</p>
                            </div>

                            <!-- First Name -->
                            <div>
                                <label for="{{ user_form.first_name.id_for_label }}" class="block text-sm font-medium text-gray-700">
                                    First Name
                                </label>
                                {{ user_form.first_name }}
                                {% if user_form.first_name.errors %}
                                    <div class="mt-1 text-sm text-red-600">
                                        {% for error in user_form.first_name.errors %}
                                            <p>{{ error }}</p>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Last Name -->
                            <div>
                                <label for="{{ user_form.last_name.id_for_label }}" class="block text-sm font-medium text-gray-700">
                                    Last Name
                                </label>
                                {{ user_form.last_name }}
                                {% if user_form.last_name.errors %}
                                    <div class="mt-1 text-sm text-red-600">
                                        {% for error in user_form.last_name.errors %}
                                            <p>{{ error }}</p>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Email -->
                            <div class="md:col-span-2">
                                <label for="{{ user_form.email.id_for_label }}" class="block text-sm font-medium text-gray-700">
                                    Email Address *
                                </label>
                                {{ user_form.email }}
                                <div x-show="emailValidation.message" 
                                     :class="emailValidation.valid ? 'text-green-600' : 'text-red-600'"
                                     class="mt-1 text-sm">
                                    <span x-text="emailValidation.message"></span>
                                </div>
                                {% if user_form.email.errors %}
                                    <div class="mt-1 text-sm text-red-600">
                                        {% for error in user_form.email.errors %}
                                            <p>{{ error }}</p>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Phone Number -->
                            <div class="md:col-span-2">
                                <label for="{{ profile_form.phone_number.id_for_label }}" class="block text-sm font-medium text-gray-700">
                                    Phone Number *
                                </label>
                                {{ profile_form.phone_number }}
                                {% if profile_form.phone_number.errors %}
                                    <div class="mt-1 text-sm text-red-600">
                                        {% for error in profile_form.phone_number.errors %}
                                            <p>{{ error }}</p>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Address -->
                            <div class="md:col-span-2">
                                <label for="{{ profile_form.address.id_for_label }}" class="block text-sm font-medium text-gray-700">
                                    Delivery Address *
                                </label>
                                {{ profile_form.address }}
                                {% if profile_form.address.errors %}
                                    <div class="mt-1 text-sm text-red-600">
                                        {% for error in profile_form.address.errors %}
                                            <p>{{ error }}</p>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Delivery Instructions -->
                            <div class="md:col-span-2">
                                <label for="{{ profile_form.delivery_instructions.id_for_label }}" class="block text-sm font-medium text-gray-700">
                                    Delivery Instructions
                                </label>
                                {{ profile_form.delivery_instructions }}
                                <p class="mt-1 text-xs text-gray-500">Optional: Any special instructions for delivery</p>
                                {% if profile_form.delivery_instructions.errors %}
                                    <div class="mt-1 text-sm text-red-600">
                                        {% for error in profile_form.delivery_instructions.errors %}
                                            <p>{{ error }}</p>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="mt-6 flex justify-end space-x-3">
                            <button type="button" 
                                    @click="resetForm()"
                                    class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-prycegas-orange">
                                Reset
                            </button>
                            <button type="submit" 
                                    :disabled="isSubmitting"
                                    :class="isSubmitting ? 'bg-gray-400 cursor-not-allowed' : 'bg-prycegas-orange hover:bg-prycegas-orange-dark focus:ring-prycegas-orange'"
                                    class="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200">
                                <span x-text="isSubmitting ? 'Saving...' : 'Save Changes'"></span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Account Summary -->
        <div class="space-y-6">
            <!-- Account Info -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Account Summary</h3>
                    <dl class="space-y-3">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Member Since</dt>
                            <dd class="text-sm text-gray-900">{{ user.date_joined|date:"F d, Y" }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Last Updated</dt>
                            <dd class="text-sm text-gray-900">{{ customer_profile.updated_at|date:"F d, Y" }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Account Status</dt>
                            <dd class="text-sm">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    Active
                                </span>
                            </dd>
                        </div>
                    </dl>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
                    <div class="space-y-3">
                        <a href="{% url 'core:place_order' %}" 
                           class="w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-prycegas-orange hover:bg-prycegas-orange-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-prycegas-orange">
                            <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                            </svg>
                            Place New Order
                        </a>
                        <a href="{% url 'core:order_history' %}" 
                           class="w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-prycegas-orange">
                            <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                            </svg>
                            View Order History
                        </a>
                        <a href="{% url 'core:customer_dashboard' %}" 
                           class="w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-prycegas-orange">
                            <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"/>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h2a2 2 0 012 2v2H8V5z"/>
                            </svg>
                            Go to Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function profileForm() {
    return {
        emailValidation: { valid: null, message: '' },
        isSubmitting: false,
        originalFormData: {},
        
        init() {
            // Store original form data
            this.storeOriginalData();
            
            // Email validation
            this.$watch('$refs.email?.value', (value) => {
                if (value && this.isValidEmail(value)) {
                    this.validateEmail(value);
                } else if (value) {
                    this.emailValidation = { valid: false, message: 'Please enter a valid email address.' };
                } else {
                    this.emailValidation = { valid: null, message: '' };
                }
            });
        },

        storeOriginalData() {
            const form = this.$el;
            const formData = new FormData(form);
            this.originalFormData = {};
            for (let [key, value] of formData.entries()) {
                if (key !== 'csrfmiddlewaretoken') {
                    this.originalFormData[key] = value;
                }
            }
        },

        async validateEmail(email) {
            try {
                const response = await fetch('{% url "core:validate_email" %}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                    },
                    body: `email=${encodeURIComponent(email)}&user_id={{ user.id }}`
                });
                const data = await response.json();
                this.emailValidation = data;
            } catch (error) {
                console.error('Email validation error:', error);
            }
        },

        isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        },

        resetForm() {
            // Reset form to original values
            for (let [key, value] of Object.entries(this.originalFormData)) {
                const field = document.querySelector(`[name="${key}"]`);
                if (field) {
                    field.value = value;
                }
            }
            this.emailValidation = { valid: null, message: '' };
            
            this.$dispatch('toast', {
                id: Date.now(),
                type: 'info',
                title: 'Form Reset',
                message: 'Form has been reset to original values.'
            });
        },

        handleSubmit(event) {
            // Basic client-side validation
            const email = document.querySelector('[name="email"]').value.trim();
            const phoneNumber = document.querySelector('[name="phone_number"]').value.trim();
            const address = document.querySelector('[name="address"]').value.trim();

            if (!email || !phoneNumber || !address) {
                event.preventDefault();
                this.$dispatch('toast', {
                    id: Date.now(),
                    type: 'error',
                    title: 'Validation Error',
                    message: 'Please fill in all required fields.'
                });
                return;
            }

            if (this.emailValidation.valid === false) {
                event.preventDefault();
                this.$dispatch('toast', {
                    id: Date.now(),
                    type: 'error',
                    title: 'Validation Error',
                    message: 'Please fix the email validation error.'
                });
                return;
            }

            this.isSubmitting = true;
        }
    }
}
</script>
{% endblock %}