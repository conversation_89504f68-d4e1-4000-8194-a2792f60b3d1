<!-- Delivery Logging Modal Content -->
<div x-data="deliveryForm()" class="relative">
    <!-- Modal Header -->
    <div class="flex items-center justify-between pb-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Log New Delivery</h3>
        <button @click="$parent.closeDeliveryModal()" 
                class="text-gray-400 hover:text-gray-600 focus:outline-none">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
        </button>
    </div>

    <!-- Form Content -->
    <div class="py-6">
        <form hx-post="{% url 'core:log_delivery' %}" 
              hx-target="#delivery-modal-content"
              hx-swap="innerHTML"
              @submit.prevent="submitForm($event)">
            {% csrf_token %}
            
            <div class="space-y-6">
                <!-- Product Selection -->
                <div>
                    <label for="{{ form.product.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Product
                    </label>
                    <select name="{{ form.product.name }}" 
                            id="{{ form.product.id_for_label }}"
                            x-model="selectedProduct"
                            @change="calculateTotal()"
                            class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-prycegas-orange focus:border-prycegas-orange"
                            required>
                        <option value="">Select a product</option>
                        {% for product in products %}
                        <option value="{{ product.id }}" data-price="{{ product.price }}">
                            {{ product.name }} - {{ product.size }} (Current: {{ product.current_stock }} units)
                        </option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Quantity and Supplier Row -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="{{ form.quantity_received.id_for_label }}" class="block text-sm font-medium text-gray-700">
                            Quantity Received
                        </label>
                        <input type="number" 
                               name="{{ form.quantity_received.name }}" 
                               id="{{ form.quantity_received.id_for_label }}"
                               x-model="quantity"
                               @input="calculateTotal()"
                               min="1"
                               class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-prycegas-orange focus:border-prycegas-orange"
                               placeholder="Enter quantity received"
                               required>
                    </div>

                    <div>
                        <label for="{{ form.supplier.id_for_label }}" class="block text-sm font-medium text-gray-700">
                            Supplier/Distributor
                        </label>
                        <input type="text" 
                               name="{{ form.supplier.name }}" 
                               id="{{ form.supplier.id_for_label }}"
                               class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-prycegas-orange focus:border-prycegas-orange"
                               placeholder="Enter supplier/distributor name"
                               required>
                    </div>
                </div>

                <!-- Delivery Date -->
                <div>
                    <label for="{{ form.delivery_date.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Delivery Date & Time
                    </label>
                    <input type="datetime-local" 
                           name="{{ form.delivery_date.name }}" 
                           id="{{ form.delivery_date.id_for_label }}"
                           value="{{ form.delivery_date.initial }}"
                           class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-prycegas-orange focus:border-prycegas-orange"
                           required>
                </div>

                <!-- Cost Information Row -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="{{ form.cost_per_unit.id_for_label }}" class="block text-sm font-medium text-gray-700">
                            Cost per Unit (₱)
                        </label>
                        <input type="number" 
                               name="{{ form.cost_per_unit.name }}" 
                               id="{{ form.cost_per_unit.id_for_label }}"
                               x-model="costPerUnit"
                               @input="calculateTotal()"
                               step="0.01"
                               min="0.01"
                               class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-prycegas-orange focus:border-prycegas-orange"
                               placeholder="Enter cost per unit"
                               required>
                    </div>

                    <div>
                        <label for="{{ form.total_cost.id_for_label }}" class="block text-sm font-medium text-gray-700">
                            Total Cost (₱)
                        </label>
                        <input type="number" 
                               name="{{ form.total_cost.name }}" 
                               id="{{ form.total_cost.id_for_label }}"
                               x-model="totalCost"
                               step="0.01"
                               readonly
                               class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-50 placeholder-gray-400 focus:outline-none focus:ring-prycegas-orange focus:border-prycegas-orange"
                               placeholder="Auto-calculated">
                    </div>
                </div>

                <!-- Notes -->
                <div>
                    <label for="{{ form.notes.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Notes (Optional)
                    </label>
                    <textarea name="{{ form.notes.name }}" 
                              id="{{ form.notes.id_for_label }}"
                              rows="3"
                              class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-prycegas-orange focus:border-prycegas-orange"
                              placeholder="Any additional notes about the delivery (optional)"></textarea>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200 mt-6">
                <button type="button" 
                        @click="$parent.closeDeliveryModal()"
                        class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-prycegas-orange">
                    Cancel
                </button>
                <button type="submit"
                        :disabled="!isFormValid()"
                        :class="isFormValid() ? 'bg-prycegas-orange hover:bg-orange-600' : 'bg-gray-300 cursor-not-allowed'"
                        class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-prycegas-orange">
                    <span x-show="!submitting">Log Delivery</span>
                    <span x-show="submitting" class="flex items-center">
                        <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Processing...
                    </span>
                </button>
            </div>
        </form>
    </div>
</div>

<script>
function deliveryForm() {
    return {
        selectedProduct: '',
        quantity: 1,
        costPerUnit: 0,
        totalCost: 0,
        submitting: false,

        calculateTotal() {
            if (this.quantity && this.costPerUnit) {
                this.totalCost = (parseFloat(this.quantity) * parseFloat(this.costPerUnit)).toFixed(2);
            } else {
                this.totalCost = 0;
            }
        },

        isFormValid() {
            return this.selectedProduct && this.quantity > 0 && this.costPerUnit > 0;
        },

        submitForm(event) {
            if (!this.isFormValid()) {
                return;
            }

            this.submitting = true;
            
            // Let HTMX handle the form submission
            // The form will be submitted via HTMX due to hx-post attribute
        }
    }
}
</script>