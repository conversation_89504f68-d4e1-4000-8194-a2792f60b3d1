{% extends 'base.html' %}

{% block title %}Place Order - Prycegas Station{% endblock %}

{% block content %}
<div class="max-w-2xl mx-auto space-y-6">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Place New Order</h1>
                    <p class="mt-1 text-sm text-gray-600">Select your LPG product and delivery preferences</p>
                </div>
                <a href="{% url 'core:customer_dashboard' %}" 
                   class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-prycegas-orange">
                    ← Back to Dashboard
                </a>
            </div>
        </div>
    </div>

    <!-- Order Form -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <form method="post" 
                  hx-post="{% url 'core:place_order' %}"
                  hx-target="#form-messages"
                  hx-swap="innerHTML"
                  x-data="orderForm()"
                  class="space-y-6 validate"
                  data-validate="true">
                {% csrf_token %}
                
                <!-- Form Messages -->
                <div id="form-messages"></div>
                
                <!-- Product Selection -->
                <div>
                    <label for="{{ form.product.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Select Product
                    </label>
                    <div class="mt-1">
                        {{ form.product }}
                    </div>
                    {% if form.product.errors %}
                        <p class="mt-2 text-sm text-red-600">{{ form.product.errors.0 }}</p>
                    {% endif %}
                </div>

                <!-- Quantity -->
                <div>
                    <label for="{{ form.quantity.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Quantity
                    </label>
                    <div class="mt-1">
                        {{ form.quantity }}
                    </div>
                    {% if form.quantity.errors %}
                        <p class="mt-2 text-sm text-red-600">{{ form.quantity.errors.0 }}</p>
                    {% endif %}
                </div>

                <!-- Stock Information -->
                <div id="stock-info">
                    <!-- Stock info will be loaded here via HTMX -->
                </div>

                <!-- Delivery Type -->
                <div>
                    <fieldset>
                        <legend class="text-sm font-medium text-gray-700">Delivery Option</legend>
                        <div class="mt-2 space-y-2">
                            {% for choice in form.delivery_type %}
                                <div class="flex items-center">
                                    {{ choice.tag }}
                                    <label for="{{ choice.id_for_label }}" class="ml-3 block text-sm font-medium text-gray-700">
                                        {{ choice.choice_label }}
                                        {% if choice.choice_value == 'pickup' %}
                                            <span class="text-gray-500">(Free)</span>
                                        {% elif choice.choice_value == 'delivery' %}
                                            <span class="text-gray-500">(Delivery available)</span>
                                        {% endif %}
                                    </label>
                                </div>
                            {% endfor %}
                        </div>
                    </fieldset>
                    {% if form.delivery_type.errors %}
                        <p class="mt-2 text-sm text-red-600">{{ form.delivery_type.errors.0 }}</p>
                    {% endif %}
                </div>

                <!-- Delivery Address -->
                <div x-show="deliveryType === 'delivery'" x-transition>
                    <label for="{{ form.delivery_address.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Delivery Address <span class="text-red-500">*</span>
                    </label>
                    <div class="mt-1">
                        {{ form.delivery_address }}
                    </div>
                    <p class="mt-2 text-sm text-gray-500">
                        Please provide a complete address with landmarks for easier delivery.
                    </p>
                    {% if form.delivery_address.errors %}
                        <p class="mt-2 text-sm text-red-600">{{ form.delivery_address.errors.0 }}</p>
                    {% endif %}
                </div>

                <!-- Notes -->
                <div>
                    <label for="{{ form.notes.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Special Instructions
                    </label>
                    <div class="mt-1">
                        {{ form.notes }}
                    </div>
                    <p class="mt-2 text-sm text-gray-500">
                        Any special instructions for delivery or handling (optional).
                    </p>
                    {% if form.notes.errors %}
                        <p class="mt-2 text-sm text-red-600">{{ form.notes.errors.0 }}</p>
                    {% endif %}
                </div>

                <!-- Submit Button -->
                <div class="flex justify-end space-x-3">
                    <a href="{% url 'core:customer_dashboard' %}" 
                       class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-prycegas-orange">
                        Cancel
                    </a>
                    <button type="submit" 
                            x-bind:disabled="isSubmitting"
                            :class="isSubmitting ? 'opacity-50 cursor-not-allowed' : ''"
                            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-prycegas-orange hover:bg-prycegas-orange-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-prycegas-orange disabled:opacity-50 disabled:cursor-not-allowed">
                        <svg x-show="!isSubmitting" class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                        </svg>
                        <svg x-show="isSubmitting" class="animate-spin -ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span x-text="isSubmitting ? 'Placing Order...' : 'Place Order'"></span>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Available Products Info -->
    {% if products %}
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Available Products</h3>
            <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                {% for product in products %}
                <div class="border border-gray-200 rounded-lg p-4">
                    <div class="flex justify-between items-start">
                        <div>
                            <h4 class="text-sm font-medium text-gray-900">{{ product.name }}</h4>
                            <p class="text-sm text-gray-500">{{ product.size }}</p>
                            <p class="text-lg font-semibold text-prycegas-orange mt-1">₱{{ product.price|floatformat:2 }}</p>
                        </div>
                        <div class="text-right">
                            <p class="text-sm text-gray-500">Stock:</p>
                            <p class="text-sm font-medium {% if product.current_stock > product.minimum_stock %}text-green-600{% elif product.current_stock > 0 %}text-yellow-600{% else %}text-red-600{% endif %}">
                                {{ product.current_stock }} units
                            </p>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    {% endif %}
</div>

<script>
function orderForm() {
    return {
        deliveryType: '{{ form.delivery_type.value|default:"delivery" }}',
        isSubmitting: false,
        
        init() {
            // Watch for delivery type changes to show/hide address field
            this.$watch('deliveryType', (value) => {
                const addressField = document.querySelector('[name="delivery_address"]');
                if (addressField) {
                    if (value === 'delivery') {
                        addressField.setAttribute('required', 'required');
                    } else {
                        addressField.removeAttribute('required');
                    }
                }
            });
        },

        handleSubmit(event) {
            // Check rate limiting
            if (!formSubmissionLimiter.canMakeRequest()) {
                event.preventDefault();
                showToast('error', 'Rate Limit Exceeded', 'Please wait before submitting again.');
                return;
            }

            this.isSubmitting = true;
        }
    }
}

// Handle HTMX form submission responses
document.addEventListener('htmx:afterRequest', function(event) {
    // Reset form submission state
    const form = event.detail.elt;
    if (form && form.querySelector('[x-data]')) {
        const alpineData = Alpine.$data(form);
        if (alpineData && alpineData.isSubmitting !== undefined) {
            alpineData.isSubmitting = false;
        }
    }

    if (event.detail.xhr.status === 200) {
        try {
            const response = JSON.parse(event.detail.xhr.responseText);
            if (response.success) {
                showToast('success', 'Order Placed', response.message);
                if (response.redirect) {
                    setTimeout(() => {
                        window.location.href = response.redirect;
                    }, 1500);
                }
            } else {
                showToast('error', 'Order Failed', response.message);
            }
        } catch (e) {
            // If response is not JSON, it might be HTML (form with errors)
            // The form will be updated with the error content
        }
    } else {
        try {
            const response = JSON.parse(event.detail.xhr.responseText);
            showToast('error', 'Order Failed', response.message);
        } catch (e) {
            showToast('error', 'Order Failed', 'An error occurred while placing your order.');
        }
    }
});
</script>
{% endblock %}