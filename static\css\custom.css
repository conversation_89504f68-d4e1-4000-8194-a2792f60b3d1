/* Custom CSS for Prycegas Station */

/* Additional custom styles can be added here */
.prycegas-gradient {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
}

/* Form focus states */
.form-input:focus {
    border-color: #ff6b35;
    box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
}

/* Loading spinner */
.spinner {
    border: 2px solid #f3f3f3;
    border-top: 2px solid #ff6b35;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive utilities */
@media (max-width: 640px) {
    .mobile-padding {
        padding-left: 1rem;
        padding-right: 1rem;
    }
}