# Generated by Django 5.2.4 on 2025-08-02 03:18

import django.core.validators
import django.db.models.deletion
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='LPGProduct',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Product name (e.g., LPG Gas)', max_length=100)),
                ('size', models.CharField(help_text='Product size (e.g., 11kg, 22kg)', max_length=50)),
                ('price', models.DecimalField(decimal_places=2, help_text='Price per unit', max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('current_stock', models.IntegerField(default=0, help_text='Current available stock', validators=[django.core.validators.MinValueValidator(0)])),
                ('minimum_stock', models.IntegerField(default=10, help_text='Minimum stock level for low stock alerts', validators=[django.core.validators.MinValueValidator(0)])),
                ('is_active', models.BooleanField(default=True, help_text='Product availability status')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'LPG Product',
                'verbose_name_plural': 'LPG Products',
                'ordering': ['name', 'size'],
            },
        ),
        migrations.CreateModel(
            name='CustomerProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('phone_number', models.CharField(help_text='Customer contact number', max_length=15)),
                ('address', models.TextField(help_text='Customer delivery address')),
                ('delivery_instructions', models.TextField(blank=True, help_text='Special delivery instructions (optional)')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='customer_profile', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Customer Profile',
                'verbose_name_plural': 'Customer Profiles',
            },
        ),
        migrations.CreateModel(
            name='DeliveryLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity_received', models.IntegerField(help_text='Number of units received', validators=[django.core.validators.MinValueValidator(1)])),
                ('supplier', models.CharField(help_text='Name of the supplier/distributor', max_length=100)),
                ('delivery_date', models.DateTimeField(help_text='Date when delivery was received')),
                ('cost_per_unit', models.DecimalField(decimal_places=2, help_text='Cost per unit from supplier', max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('total_cost', models.DecimalField(decimal_places=2, help_text='Total cost of the delivery', max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('notes', models.TextField(blank=True, help_text='Additional notes about the delivery')),
                ('logged_by', models.ForeignKey(help_text='Staff member who logged this delivery', on_delete=django.db.models.deletion.CASCADE, related_name='delivery_logs', to=settings.AUTH_USER_MODEL)),
                ('product', models.ForeignKey(help_text='Product that was delivered', on_delete=django.db.models.deletion.CASCADE, related_name='delivery_logs', to='core.lpgproduct')),
            ],
            options={
                'verbose_name': 'Delivery Log',
                'verbose_name_plural': 'Delivery Logs',
                'ordering': ['-delivery_date'],
            },
        ),
        migrations.CreateModel(
            name='Order',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.IntegerField(help_text='Number of units ordered', validators=[django.core.validators.MinValueValidator(1)])),
                ('delivery_type', models.CharField(choices=[('pickup', 'Pickup'), ('delivery', 'Delivery')], help_text='Pickup or delivery option', max_length=20)),
                ('delivery_address', models.TextField(help_text='Address for delivery orders')),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('out_for_delivery', 'Out for Delivery'), ('delivered', 'Delivered'), ('cancelled', 'Cancelled')], default='pending', help_text='Current order status', max_length=20)),
                ('total_amount', models.DecimalField(decimal_places=2, help_text='Total order amount', max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('order_date', models.DateTimeField(auto_now_add=True)),
                ('delivery_date', models.DateTimeField(blank=True, help_text='Date when order was delivered', null=True)),
                ('notes', models.TextField(blank=True, help_text='Additional order notes or instructions')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('customer', models.ForeignKey(help_text='Customer who placed the order', on_delete=django.db.models.deletion.CASCADE, related_name='orders', to=settings.AUTH_USER_MODEL)),
                ('product', models.ForeignKey(help_text='Ordered product', on_delete=django.db.models.deletion.CASCADE, related_name='orders', to='core.lpgproduct')),
            ],
            options={
                'verbose_name': 'Order',
                'verbose_name_plural': 'Orders',
                'ordering': ['-order_date'],
            },
        ),
    ]
