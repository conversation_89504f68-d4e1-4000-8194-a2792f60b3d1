{% extends 'base.html' %}
{% load static %}

{% block title %}Sales Report - Prycegas Station{% endblock %}

{% block extra_css %}
<style>
@media print {
    .no-print { display: none !important; }
    .print-only { display: block !important; }
    body { font-size: 12px; }
    .bg-orange-500 { background-color: #f97316 !important; -webkit-print-color-adjust: exact; }
    .text-white { color: white !important; -webkit-print-color-adjust: exact; }
}
.print-only { display: none; }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
    <!-- Include Sidebar (hidden on print) -->
    <div class="no-print">
        {% include 'components/sidebar.html' %}
    </div>
    
    <!-- Main Content -->
    <div class="lg:ml-64 print:ml-0">
        <!-- Header -->
        <div class="bg-white shadow-sm border-b border-gray-200 no-print">
            <div class="px-4 sm:px-6 lg:px-8 py-4">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">Sales Report</h1>
                        <p class="text-sm text-gray-600 mt-1">
                            {% if filters.date_from and filters.date_to %}
                                {{ filters.date_from }} to {{ filters.date_to }}
                            {% else %}
                                Last 30 days
                            {% endif %}
                        </p>
                    </div>
                    <div class="flex items-center space-x-3">
                        <button onclick="window.print()" 
                                class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md font-medium transition-colors duration-200">
                            Print Report
                        </button>
                        <a href="{% url 'core:reports_dashboard' %}" 
                           class="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-md font-medium transition-colors duration-200">
                            Back to Reports
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Print Header -->
        <div class="print-only bg-orange-500 text-white p-4 mb-6">
            <div class="text-center">
                <h1 class="text-2xl font-bold">Vios Prycegas Tambulig Station</h1>
                <h2 class="text-lg">Sales Report</h2>
                <p class="text-sm mt-2">
                    Period: {{ filters.date_from }} to {{ filters.date_to }}
                    {% if filters.product %}
                        | Product Filter Applied
                    {% endif %}
                    {% if filters.customer %}
                        | Customer Filter Applied
                    {% endif %}
                </p>
                <p class="text-xs mt-1">Generated on: {% now "F d, Y g:i A" %}</p>
            </div>
        </div>

        <div class="px-4 sm:px-6 lg:px-8 py-6 print:px-4 print:py-2">
            <!-- Filters (hidden on print) -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6 no-print">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">Filter Options</h3>
                </div>
                <div class="p-6">
                    <form method="get" class="space-y-4">
                        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                            <div>
                                <label for="date_from" class="block text-sm font-medium text-gray-700 mb-1">From Date</label>
                                <input type="date" id="date_from" name="date_from" value="{{ filters.date_from }}"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent">
                            </div>
                            <div>
                                <label for="date_to" class="block text-sm font-medium text-gray-700 mb-1">To Date</label>
                                <input type="date" id="date_to" name="date_to" value="{{ filters.date_to }}"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent">
                            </div>
                            <div>
                                <label for="product" class="block text-sm font-medium text-gray-700 mb-1">Product</label>
                                <select id="product" name="product" 
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent">
                                    <option value="">All Products</option>
                                    {% for product in products %}
                                        <option value="{{ product.id }}" {% if filters.product == product.id|stringformat:"s" %}selected{% endif %}>
                                            {{ product.name }} - {{ product.size }}
                                        </option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div>
                                <label for="customer" class="block text-sm font-medium text-gray-700 mb-1">Customer</label>
                                <select id="customer" name="customer" 
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent">
                                    <option value="">All Customers</option>
                                    {% for customer in customers %}
                                        <option value="{{ customer.id }}" {% if filters.customer == customer.id|stringformat:"s" %}selected{% endif %}>
                                            {{ customer.username }}
                                        </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="flex justify-end">
                            <button type="submit" 
                                    class="bg-orange-500 hover:bg-orange-600 text-white px-6 py-2 rounded-md font-medium transition-colors duration-200">
                                Update Report
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Summary Statistics -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 print:shadow-none print:border-2">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-orange-600">{{ summary.total_orders }}</div>
                        <div class="text-sm text-gray-600">Total Orders</div>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 print:shadow-none print:border-2">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-green-600">₱{{ summary.total_revenue|floatformat:2 }}</div>
                        <div class="text-sm text-gray-600">Total Revenue</div>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 print:shadow-none print:border-2">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-blue-600">{{ summary.total_quantity }}</div>
                        <div class="text-sm text-gray-600">Units Sold</div>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 print:shadow-none print:border-2">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-purple-600">₱{{ summary.average_order_value|floatformat:2 }}</div>
                        <div class="text-sm text-gray-600">Avg Order Value</div>
                    </div>
                </div>
            </div>

            <!-- Product Breakdown -->
            {% if product_stats %}
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8 print:shadow-none print:border-2">
                <div class="px-6 py-4 border-b border-gray-200 bg-orange-50 print:bg-orange-500 print:text-white">
                    <h3 class="text-lg font-semibold text-gray-900 print:text-white">Sales by Product</h3>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50 print:bg-gray-200">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Orders</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Revenue</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for stat in product_stats %}
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                    {{ stat.product__name }} - {{ stat.product__size }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ stat.order_count }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ stat.total_quantity }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">₱{{ stat.total_revenue|floatformat:2 }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
            {% endif %}

            <!-- Customer Breakdown -->
            {% if customer_stats %}
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8 print:shadow-none print:border-2">
                <div class="px-6 py-4 border-b border-gray-200 bg-orange-50 print:bg-orange-500 print:text-white">
                    <h3 class="text-lg font-semibold text-gray-900 print:text-white">Top Customers</h3>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50 print:bg-gray-200">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Orders</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Spent</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for stat in customer_stats|slice:":10" %}
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                    {{ stat.customer__username }}
                                    {% if stat.customer__first_name %}
                                        ({{ stat.customer__first_name }} {{ stat.customer__last_name }})
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ stat.total_orders }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ stat.total_quantity }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">₱{{ stat.total_spent|floatformat:2 }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
            {% endif %}

            <!-- Detailed Orders -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 print:shadow-none print:border-2">
                <div class="px-6 py-4 border-b border-gray-200 bg-orange-50 print:bg-orange-500 print:text-white">
                    <h3 class="text-lg font-semibold text-gray-900 print:text-white">Order Details</h3>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50 print:bg-gray-200">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order ID</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Qty</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for order in orders %}
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">#{{ order.id }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ order.delivery_date|date:"M d, Y" }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ order.customer.username }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ order.product.name }} - {{ order.product.size }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ order.quantity }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">₱{{ order.total_amount|floatformat:2 }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        {% if order.delivery_type == 'delivery' %}bg-blue-100 text-blue-800{% else %}bg-green-100 text-green-800{% endif %}">
                                        {{ order.get_delivery_type_display }}
                                    </span>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="7" class="px-6 py-4 text-center text-sm text-gray-500">
                                    No orders found for the selected criteria.
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Print Footer -->
            <div class="print-only mt-8 text-center text-xs text-gray-600">
                <p>This report was generated automatically by the Prycegas Station Management System</p>
                <p>For questions or concerns, please contact the station administrator</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}