{% extends 'base.html' %}
{% load static %}

{% block title %}Stock Report - Prycegas Station{% endblock %}

{% block extra_css %}
<style>
@media print {
    .no-print { display: none !important; }
    .print-only { display: block !important; }
    body { font-size: 12px; }
    .bg-blue-500 { background-color: #3b82f6 !important; -webkit-print-color-adjust: exact; }
    .text-white { color: white !important; -webkit-print-color-adjust: exact; }
}
.print-only { display: none; }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
    <!-- Include Sidebar (hidden on print) -->
    <div class="no-print">
        {% include 'components/sidebar.html' %}
    </div>
    
    <!-- Main Content -->
    <div class="lg:ml-64 print:ml-0">
        <!-- Header -->
        <div class="bg-white shadow-sm border-b border-gray-200 no-print">
            <div class="px-4 sm:px-6 lg:px-8 py-4">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">Stock Report</h1>
                        <p class="text-sm text-gray-600 mt-1">
                            {% if filters.date_from and filters.date_to %}
                                Movement period: {{ filters.date_from }} to {{ filters.date_to }}
                            {% else %}
                                Last 30 days movement
                            {% endif %}
                        </p>
                    </div>
                    <div class="flex items-center space-x-3">
                        <button onclick="window.print()" 
                                class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md font-medium transition-colors duration-200">
                            Print Report
                        </button>
                        <a href="{% url 'core:reports_dashboard' %}" 
                           class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md font-medium transition-colors duration-200">
                            Back to Reports
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Print Header -->
        <div class="print-only bg-blue-500 text-white p-4 mb-6">
            <div class="text-center">
                <h1 class="text-2xl font-bold">Vios Prycegas Tambulig Station</h1>
                <h2 class="text-lg">Stock & Inventory Report</h2>
                <p class="text-sm mt-2">
                    Movement Period: {{ filters.date_from }} to {{ filters.date_to }}
                    {% if filters.product %}
                        | Product Filter Applied
                    {% endif %}
                </p>
                <p class="text-xs mt-1">Generated on: {% now "F d, Y g:i A" %}</p>
            </div>
        </div>

        <div class="px-4 sm:px-6 lg:px-8 py-6 print:px-4 print:py-2">
            <!-- Filters (hidden on print) -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6 no-print">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">Filter Options</h3>
                </div>
                <div class="p-6">
                    <form method="get" class="space-y-4">
                        <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
                            <div>
                                <label for="date_from" class="block text-sm font-medium text-gray-700 mb-1">From Date</label>
                                <input type="date" id="date_from" name="date_from" value="{{ filters.date_from }}"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                            <div>
                                <label for="date_to" class="block text-sm font-medium text-gray-700 mb-1">To Date</label>
                                <input type="date" id="date_to" name="date_to" value="{{ filters.date_to }}"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                            <div>
                                <label for="product" class="block text-sm font-medium text-gray-700 mb-1">Product</label>
                                <select id="product" name="product" 
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <option value="">All Products</option>
                                    {% for product in products %}
                                        <option value="{{ product.id }}" {% if filters.product == product.id|stringformat:"s" %}selected{% endif %}>
                                            {{ product.name }} - {{ product.size }}
                                        </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="flex justify-end">
                            <button type="submit" 
                                    class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-md font-medium transition-colors duration-200">
                                Update Report
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Current Inventory Summary -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 print:shadow-none print:border-2">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-blue-600">₱{{ inventory_summary.total_stock_value|floatformat:2 }}</div>
                        <div class="text-sm text-gray-600">Total Stock Value</div>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 print:shadow-none print:border-2">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-green-600">{{ inventory_summary.total_current_stock }}</div>
                        <div class="text-sm text-gray-600">Total Units in Stock</div>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 print:shadow-none print:border-2">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-yellow-600">{{ inventory_summary.low_stock_products }}</div>
                        <div class="text-sm text-gray-600">Low Stock Items</div>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 print:shadow-none print:border-2">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-red-600">{{ inventory_summary.out_of_stock_products }}</div>
                        <div class="text-sm text-gray-600">Out of Stock</div>
                    </div>
                </div>
            </div>

            <!-- Period Summary -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8 print:shadow-none print:border-2">
                <div class="px-6 py-4 border-b border-gray-200 bg-blue-50 print:bg-blue-500 print:text-white">
                    <h3 class="text-lg font-semibold text-gray-900 print:text-white">Period Summary ({{ filters.date_from }} to {{ filters.date_to }})</h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="text-center">
                            <div class="text-xl font-bold text-green-600">{{ period_summary.total_deliveries }}</div>
                            <div class="text-sm text-gray-600">Deliveries Received</div>
                            <div class="text-xs text-gray-500 mt-1">{{ period_summary.total_delivered_quantity }} units</div>
                        </div>
                        <div class="text-center">
                            <div class="text-xl font-bold text-blue-600">{{ period_summary.total_sales }}</div>
                            <div class="text-sm text-gray-600">Orders Delivered</div>
                            <div class="text-xs text-gray-500 mt-1">{{ period_summary.total_sold_quantity }} units</div>
                        </div>
                        <div class="text-center">
                            <div class="text-xl font-bold text-purple-600">₱{{ period_summary.total_delivery_cost|floatformat:2 }}</div>
                            <div class="text-sm text-gray-600">Delivery Costs</div>
                            <div class="text-xs text-gray-500 mt-1">₱{{ period_summary.total_sales_revenue|floatformat:2 }} revenue</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Product Details -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8 print:shadow-none print:border-2">
                <div class="px-6 py-4 border-b border-gray-200 bg-blue-50 print:bg-blue-500 print:text-white">
                    <h3 class="text-lg font-semibold text-gray-900 print:text-white">Product Inventory Details</h3>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50 print:bg-gray-200">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Current Stock</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Min Stock</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Stock Value</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Delivered</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sold</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Net Movement</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for detail in product_details %}
                            <tr {% if detail.is_out_of_stock %}class="bg-red-50"{% elif detail.is_low_stock %}class="bg-yellow-50"{% endif %}>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                    {{ detail.product.name }} - {{ detail.product.size }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ detail.current_stock }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ detail.minimum_stock }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">₱{{ detail.stock_value|floatformat:2 }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <span class="text-green-600">+{{ detail.delivered_qty }}</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <span class="text-red-600">-{{ detail.sold_qty }}</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {% if detail.net_movement > 0 %}
                                        <span class="text-green-600">+{{ detail.net_movement }}</span>
                                    {% elif detail.net_movement < 0 %}
                                        <span class="text-red-600">{{ detail.net_movement }}</span>
                                    {% else %}
                                        <span class="text-gray-500">0</span>
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {% if detail.is_out_of_stock %}
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            Out of Stock
                                        </span>
                                    {% elif detail.is_low_stock %}
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                            Low Stock
                                        </span>
                                    {% else %}
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            In Stock
                                        </span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="8" class="px-6 py-4 text-center text-sm text-gray-500">
                                    No products found for the selected criteria.
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Recent Stock Movements -->
            {% if recent_movements %}
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 print:shadow-none print:border-2">
                <div class="px-6 py-4 border-b border-gray-200 bg-blue-50 print:bg-blue-500 print:text-white">
                    <h3 class="text-lg font-semibold text-gray-900 print:text-white">Recent Stock Movements</h3>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50 print:bg-gray-200">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cost per Unit</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Cost</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Logged By</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for movement in recent_movements %}
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ movement.delivery_date|date:"M d, Y" }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                    {{ movement.product.name }} - {{ movement.product.size }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ movement.supplier }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <span class="text-green-600">+{{ movement.quantity_received }}</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">₱{{ movement.cost_per_unit|floatformat:2 }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">₱{{ movement.total_cost|floatformat:2 }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ movement.logged_by.username }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
            {% endif %}

            <!-- Print Footer -->
            <div class="print-only mt-8 text-center text-xs text-gray-600">
                <p>This report was generated automatically by the Prycegas Station Management System</p>
                <p>For questions or concerns, please contact the station administrator</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}