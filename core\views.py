from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import login, authenticate, logout
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib import messages
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_protect
from django.contrib.auth.models import User
from django.db import transaction
from django.db.models import Count, Sum, Q, F, Avg
from django.utils import timezone
from datetime import datetime, timedelta
from .forms import CustomerRegistrationForm, CustomerLoginForm, CustomerProfileForm, UserUpdateForm, OrderForm, DeliveryLogForm
from .models import CustomerProfile, LPGProduct, Order, DeliveryLog


def test_base_template(request):
    """Test view to verify base template functionality"""
    return render(request, 'test_base.html')


# Authentication Views
@csrf_protect
def customer_register(request):
    """
    Customer registration view with form validation
    Requirements: 1.1, 1.2, 1.3 - Customer registration with validation
    """
    if request.user.is_authenticated:
        return redirect('core:customer_dashboard')
    
    if request.method == 'POST':
        form = CustomerRegistrationForm(request.POST)
        if form.is_valid():
            try:
                user = form.save()
                username = form.cleaned_data.get('username')
                messages.success(request, f'Account created successfully for {username}! You can now log in.')
                
                # Auto-login the user after registration
                user = authenticate(
                    username=form.cleaned_data['username'],
                    password=form.cleaned_data['password1']
                )
                if user:
                    login(request, user)
                    return redirect('core:customer_dashboard')
                else:
                    return redirect('core:login')
            except Exception as e:
                messages.error(request, 'An error occurred during registration. Please try again.')
        else:
            # Form has validation errors
            for field, errors in form.errors.items():
                for error in errors:
                    messages.error(request, f'{field.replace("_", " ").title()}: {error}')
    else:
        form = CustomerRegistrationForm()
    
    return render(request, 'auth/register.html', {'form': form})


@csrf_protect
def customer_login(request):
    """
    Customer login view using Django's built-in authentication
    Requirements: 1.4 - Customer login functionality
    """
    if request.user.is_authenticated:
        return redirect('core:customer_dashboard')
    
    if request.method == 'POST':
        form = CustomerLoginForm(request, data=request.POST)
        if form.is_valid():
            username = form.cleaned_data.get('username')
            password = form.cleaned_data.get('password')
            user = authenticate(username=username, password=password)
            if user is not None:
                login(request, user)
                messages.success(request, f'Welcome back, {user.username}!')
                
                # Redirect to next page if specified, otherwise to dashboard
                next_page = request.GET.get('next')
                if next_page:
                    return redirect(next_page)
                return redirect('core:customer_dashboard')
            else:
                messages.error(request, 'Invalid username or password.')
        else:
            messages.error(request, 'Please correct the errors below.')
    else:
        form = CustomerLoginForm()
    
    return render(request, 'auth/login.html', {'form': form})


@require_http_methods(["POST"])
def customer_logout(request):
    """
    Customer logout functionality
    Requirements: 1.5 - Customer logout functionality
    """
    if request.user.is_authenticated:
        username = request.user.username
        logout(request)
        messages.success(request, f'You have been logged out successfully, {username}.')
    return redirect('core:login')


@login_required
@csrf_protect
def customer_profile(request):
    """
    Customer profile view for updating profile information
    Requirements: 1.2 - Customer profile management
    """
    try:
        customer_profile = request.user.customer_profile
    except CustomerProfile.DoesNotExist:
        # Create profile if it doesn't exist
        customer_profile = CustomerProfile.objects.create(
            user=request.user,
            phone_number='',
            address='',
            delivery_instructions=''
        )
    
    if request.method == 'POST':
        user_form = UserUpdateForm(request.POST, instance=request.user)
        profile_form = CustomerProfileForm(request.POST, instance=customer_profile)
        
        if user_form.is_valid() and profile_form.is_valid():
            user_form.save()
            profile_form.save()
            messages.success(request, 'Your profile has been updated successfully!')
            return redirect('core:profile')
        else:
            # Display form errors
            for form in [user_form, profile_form]:
                for field, errors in form.errors.items():
                    for error in errors:
                        messages.error(request, f'{field.replace("_", " ").title()}: {error}')
    else:
        user_form = UserUpdateForm(instance=request.user)
        profile_form = CustomerProfileForm(instance=customer_profile)
    
    context = {
        'user_form': user_form,
        'profile_form': profile_form,
        'customer_profile': customer_profile
    }
    return render(request, 'customer/profile.html', context)


# HTMX Views for enhanced form validation
@csrf_protect
def validate_username(request):
    """
    Enhanced HTMX endpoint for real-time username validation with security
    Requirements: 10.1, 10.2, 10.3 - Form validation with security measures
    """
    if request.method == 'POST':
        username = request.POST.get('username', '').strip()
        
        # Input sanitization
        from django.utils.html import strip_tags
        username = strip_tags(username)
        
        if username:
            # Length validation
            if len(username) < 3:
                return JsonResponse({
                    'valid': False,
                    'message': 'Username must be at least 3 characters long.'
                })
            elif len(username) > 30:
                return JsonResponse({
                    'valid': False,
                    'message': 'Username cannot exceed 30 characters.'
                })
            
            # Character validation
            import re
            if not re.match(r'^[a-zA-Z0-9_]+$', username):
                return JsonResponse({
                    'valid': False,
                    'message': 'Username can only contain letters, numbers, and underscores.'
                })
            
            # Uniqueness validation
            if User.objects.filter(username__iexact=username).exists():
                return JsonResponse({
                    'valid': False,
                    'message': 'This username is already taken.'
                })
            
            return JsonResponse({
                'valid': True,
                'message': 'Username is available.'
            })
        
        return JsonResponse({
            'valid': False,
            'message': 'Username is required.'
        })
    
    return JsonResponse({'valid': False, 'message': 'Invalid request method.'})


@csrf_protect
def validate_email(request):
    """
    Enhanced HTMX endpoint for real-time email validation with security
    Requirements: 10.1, 10.2, 10.3 - Form validation with security measures
    """
    if request.method == 'POST':
        email = request.POST.get('email', '').strip()
        user_id = request.POST.get('user_id')  # For profile updates
        
        # Input sanitization
        from django.utils.html import strip_tags
        email = strip_tags(email).lower()
        
        if email:
            # Email format validation
            import re
            if not re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', email):
                return JsonResponse({
                    'valid': False,
                    'message': 'Please enter a valid email address.'
                })
            
            # Uniqueness validation
            query = User.objects.filter(email__iexact=email)
            if user_id:
                try:
                    query = query.exclude(pk=int(user_id))
                except (ValueError, TypeError):
                    pass
            
            if query.exists():
                return JsonResponse({
                    'valid': False,
                    'message': 'This email is already registered.'
                })
            
            return JsonResponse({
                'valid': True,
                'message': 'Email is available.'
            })
        
        return JsonResponse({
            'valid': False,
            'message': 'Email is required.'
        })
    
    return JsonResponse({'valid': False, 'message': 'Invalid request method.'})


# Customer Dashboard View
@login_required
def customer_dashboard(request):
    """
    Customer dashboard showing recent orders and quick actions
    Requirements: 3.1 - Customer order history display
    Optimized: Added select_related to prevent N+1 queries
    """
    # Optimized query with select_related to prevent N+1 queries
    recent_orders = Order.objects.filter(customer=request.user).select_related('product').order_by('-order_date')[:5]
    
    # Use aggregate queries for better performance
    order_stats = request.user.orders.aggregate(
        total_orders=Count('id'),
        pending_orders=Count('id', filter=Q(status='pending')),
        delivered_orders=Count('id', filter=Q(status='delivered'))
    )
    
    context = {
        'recent_orders': recent_orders,
        'total_orders': order_stats['total_orders'],
        'pending_orders': order_stats['pending_orders'],
        'delivered_orders': order_stats['delivered_orders'],
    }
    return render(request, 'customer/dashboard.html', context)


# Order Placement Views
@login_required
@csrf_protect
def place_order(request):
    """
    Customer order placement view with product selection and delivery options
    Requirements: 2.1, 2.2, 2.3, 2.4, 2.5 - Order placement system
    """
    if request.method == 'POST':
        form = OrderForm(request.POST, user=request.user)
        if form.is_valid():
            try:
                with transaction.atomic():
                    order = form.save()
                    messages.success(
                        request, 
                        f'Order #{order.id} placed successfully! Total: ₱{order.total_amount:.2f}'
                    )
                    
                    # Return JSON response for HTMX requests
                    if request.headers.get('HX-Request'):
                        return JsonResponse({
                            'success': True,
                            'message': f'Order #{order.id} placed successfully!',
                            'order_id': order.id,
                            'total_amount': float(order.total_amount),
                            'redirect': '/customer/dashboard/'
                        })
                    
                    return redirect('core:customer_dashboard')
            except Exception as e:
                messages.error(request, f'Error placing order: {str(e)}')
                if request.headers.get('HX-Request'):
                    return JsonResponse({
                        'success': False,
                        'message': f'Error placing order: {str(e)}'
                    }, status=400)
        else:
            # Handle form errors
            error_messages = []
            for field, errors in form.errors.items():
                for error in errors:
                    error_messages.append(f'{field.replace("_", " ").title()}: {error}')
            
            if request.headers.get('HX-Request'):
                return JsonResponse({
                    'success': False,
                    'message': '; '.join(error_messages)
                }, status=400)
            
            for message in error_messages:
                messages.error(request, message)
    else:
        form = OrderForm(user=request.user)
    
    # Get available products for display
    products = LPGProduct.objects.filter(is_active=True).order_by('name', 'size')
    
    context = {
        'form': form,
        'products': products,
    }
    return render(request, 'customer/place_order.html', context)


@login_required
def check_stock(request):
    """
    HTMX endpoint for real-time stock checking
    Requirements: 2.4 - Real-time inventory checking using HTMX
    """
    product_id = request.GET.get('product')
    quantity = request.GET.get('quantity', 1)
    
    try:
        quantity = int(quantity)
        if quantity < 1:
            quantity = 1
    except (ValueError, TypeError):
        quantity = 1
    
    if not product_id:
        return render(request, 'customer/stock_info.html', {
            'show_info': False
        })
    
    try:
        product = LPGProduct.objects.get(id=product_id, is_active=True)
        
        # Calculate total price
        total_price = product.price * quantity
        
        # Check stock availability
        stock_available = product.can_fulfill_order(quantity)
        stock_status = 'available' if stock_available else 'insufficient'
        
        # Determine stock level status
        if product.current_stock == 0:
            stock_level = 'out_of_stock'
        elif product.is_low_stock:
            stock_level = 'low_stock'
        else:
            stock_level = 'in_stock'
        
        context = {
            'show_info': True,
            'product': product,
            'quantity': quantity,
            'total_price': total_price,
            'stock_available': stock_available,
            'stock_status': stock_status,
            'stock_level': stock_level,
        }
        
    except LPGProduct.DoesNotExist:
        context = {
            'show_info': False,
            'error': 'Product not found'
        }
    
    return render(request, 'customer/stock_info.html', context)


@login_required
def order_history(request):
    """
    Customer order history view with filtering and status tracking
    Requirements: 3.1, 3.2, 3.3 - Order history and tracking
    Optimized: Added pagination and query optimization
    """
    from django.core.paginator import Paginator
    
    # Optimized base query with select_related
    orders = Order.objects.filter(customer=request.user).select_related('product').order_by('-order_date')
    
    # Filter by status if provided
    status_filter = request.GET.get('status')
    if status_filter and status_filter in dict(Order.STATUS_CHOICES):
        orders = orders.filter(status=status_filter)
    
    # Sort by date or status if provided
    sort_by = request.GET.get('sort', '-order_date')
    if sort_by in ['-order_date', 'order_date', '-total_amount', 'total_amount', 'status']:
        orders = orders.order_by(sort_by)
    
    # Add pagination for better performance with large datasets
    paginator = Paginator(orders, 20)  # Show 20 orders per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'orders': page_obj,
        'status_choices': Order.STATUS_CHOICES,
        'current_status': status_filter,
        'current_sort': sort_by,
        'is_paginated': page_obj.has_other_pages(),
        'page_obj': page_obj,
    }
    return render(request, 'customer/order_history.html', context)


@login_required
def order_detail(request, order_id):
    """
    Customer order detail view with delivery tracking information
    Requirements: 3.3, 3.4 - Order detail view with tracking
    """
    order = get_object_or_404(Order, id=order_id, customer=request.user)
    
    # Calculate order progress for tracking
    status_progress = {
        'pending': 25,
        'out_for_delivery': 75,
        'delivered': 100,
        'cancelled': 0
    }
    
    context = {
        'order': order,
        'progress_percentage': status_progress.get(order.status, 0),
    }
    return render(request, 'customer/order_detail.html', context)


@login_required
def refresh_order_status(request):
    """
    HTMX endpoint for real-time order status updates
    Requirements: 3.2 - Real-time order status updates using HTMX
    """
    if request.headers.get('HX-Request'):
        orders = Order.objects.filter(customer=request.user).select_related('product').order_by('-order_date')
        
        # Apply same filters as order_history
        status_filter = request.GET.get('status')
        if status_filter and status_filter in dict(Order.STATUS_CHOICES):
            orders = orders.filter(status=status_filter)
        
        sort_by = request.GET.get('sort', '-order_date')
        if sort_by in ['-order_date', 'order_date', '-total_amount', 'total_amount', 'status']:
            orders = orders.order_by(sort_by)
        
        context = {
            'orders': orders,
            'current_status': status_filter,
        }
        return render(request, 'customer/order_list_partial.html', context)
    
    return redirect('core:order_history')


# Dealer/Admin Authentication Helper
def is_dealer(user):
    """
    Check if user is a dealer/admin (staff member)
    Requirements: 4.1 - Dealer authentication and permission system
    """
    return user.is_authenticated and user.is_staff


# Dealer/Admin Dashboard Views
@user_passes_test(is_dealer, login_url='core:login')
def dealer_dashboard(request):
    """
    Main dealer dashboard with order counts, inventory levels, and recent activity
    Requirements: 4.1, 4.2, 4.3, 4.4, 4.5 - Dealer dashboard with overview
    Optimized: Combined queries and added caching for better performance
    """
    from django.core.cache import cache
    
    # Try to get cached dashboard stats first
    cache_key = 'dealer_dashboard_stats'
    dashboard_stats = cache.get(cache_key)
    
    if not dashboard_stats:
        # Calculate dashboard statistics with optimized queries
        today = timezone.now().date()
        week_ago = today - timedelta(days=7)
        
        # Use aggregate queries for better performance
        order_stats = Order.objects.aggregate(
            total_orders=Count('id'),
            pending_orders=Count('id', filter=Q(status='pending')),
            out_for_delivery=Count('id', filter=Q(status='out_for_delivery')),
            delivered_today=Count('id', filter=Q(status='delivered', delivery_date__date=today)),
            weekly_orders=Count('id', filter=Q(order_date__date__gte=week_ago)),
            weekly_revenue=Sum('total_amount', filter=Q(order_date__date__gte=week_ago, status='delivered'))
        )
        
        # Inventory statistics with single query
        inventory_stats = LPGProduct.objects.filter(is_active=True).aggregate(
            total_products=Count('id'),
            low_stock_products=Count('id', filter=Q(current_stock__lte=F('minimum_stock'))),
            out_of_stock=Count('id', filter=Q(current_stock=0)),
            total_stock_value=Sum(F('current_stock') * F('price'))
        )
        
        dashboard_stats = {
            **order_stats,
            **inventory_stats,
            'weekly_revenue': order_stats['weekly_revenue'] or 0,
            'total_stock_value': inventory_stats['total_stock_value'] or 0,
        }
        
        # Cache for 30 seconds to reduce database load
        cache.set(cache_key, dashboard_stats, 30)
    
    # Recent activity with optimized queries
    recent_orders = Order.objects.select_related('customer', 'product').order_by('-order_date')[:10]
    recent_deliveries = DeliveryLog.objects.select_related('product', 'logged_by').order_by('-created_at')[:5]
    low_stock_alerts = LPGProduct.objects.filter(
        is_active=True,
        current_stock__lte=F('minimum_stock')
    ).order_by('current_stock')[:5]
    
    context = {
        'dashboard_stats': dashboard_stats,
        'recent_orders': recent_orders,
        'recent_deliveries': recent_deliveries,
        'low_stock_alerts': low_stock_alerts,
    }
    
    return render(request, 'dealer/dashboard.html', context)


@user_passes_test(is_dealer, login_url='core:login')
def refresh_dashboard_stats(request):
    """
    HTMX endpoint for refreshing dashboard statistics with real-time updates
    Requirements: 4.4, 4.5 - Real-time dashboard updates using Unpoly
    """
    if request.headers.get('HX-Request') or request.headers.get('X-Up-Target'):
        today = timezone.now().date()
        week_ago = today - timedelta(days=7)
        
        # Recalculate statistics
        dashboard_stats = {
            'total_orders': Order.objects.count(),
            'pending_orders': Order.objects.filter(status='pending').count(),
            'out_for_delivery': Order.objects.filter(status='out_for_delivery').count(),
            'delivered_today': Order.objects.filter(
                status='delivered',
                delivery_date__date=today
            ).count(),
            'weekly_orders': Order.objects.filter(order_date__date__gte=week_ago).count(),
            'weekly_revenue': Order.objects.filter(
                order_date__date__gte=week_ago,
                status='delivered'
            ).aggregate(total=Sum('total_amount'))['total'] or 0,
            'total_products': LPGProduct.objects.filter(is_active=True).count(),
            'low_stock_products': LPGProduct.objects.filter(
                is_active=True,
                current_stock__lte=F('minimum_stock')
            ).count(),
            'out_of_stock': LPGProduct.objects.filter(
                is_active=True,
                current_stock=0
            ).count(),
            'total_stock_value': LPGProduct.objects.filter(is_active=True).aggregate(
                total=Sum(F('current_stock') * F('price'))
            )['total'] or 0,
        }
        
        context = {'dashboard_stats': dashboard_stats}
        return render(request, 'dealer/dashboard_stats_partial.html', context)
    
    return redirect('core:dealer_dashboard')


@user_passes_test(is_dealer, login_url='core:login')
def refresh_recent_activity(request):
    """
    HTMX endpoint for refreshing recent activity section
    Requirements: 4.4, 4.5 - Real-time activity updates
    """
    if request.headers.get('HX-Request') or request.headers.get('X-Up-Target'):
        recent_orders = Order.objects.select_related('customer', 'product').order_by('-order_date')[:10]
        recent_deliveries = DeliveryLog.objects.select_related('product', 'logged_by').order_by('-created_at')[:5]
        low_stock_alerts = LPGProduct.objects.filter(
            is_active=True,
            current_stock__lte=F('minimum_stock')
        ).order_by('current_stock')[:5]
        
        context = {
            'recent_orders': recent_orders,
            'recent_deliveries': recent_deliveries,
            'low_stock_alerts': low_stock_alerts,
        }
        return render(request, 'dealer/recent_activity_partial.html', context)
    
    return redirect('core:dealer_dashboard')


# Order Management Views for Dealers
@user_passes_test(is_dealer, login_url='core:login')
def order_management(request):
    """
    Order list view with sortable and filterable table for dealers
    Requirements: 5.1, 5.2, 5.3, 5.4, 5.5 - Order management system
    Optimized: Added pagination and query optimization for large datasets
    """
    from django.core.paginator import Paginator
    
    # Start with optimized base query
    orders = Order.objects.select_related('customer', 'product').all()
    
    # Filter by status
    status_filter = request.GET.get('status', '')
    if status_filter and status_filter in dict(Order.STATUS_CHOICES):
        orders = orders.filter(status=status_filter)
    
    # Filter by delivery type
    delivery_filter = request.GET.get('delivery_type', '')
    if delivery_filter and delivery_filter in dict(Order.DELIVERY_CHOICES):
        orders = orders.filter(delivery_type=delivery_filter)
    
    # Filter by date range
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')
    if date_from:
        try:
            from_date = datetime.strptime(date_from, '%Y-%m-%d').date()
            orders = orders.filter(order_date__date__gte=from_date)
        except ValueError:
            pass
    if date_to:
        try:
            to_date = datetime.strptime(date_to, '%Y-%m-%d').date()
            orders = orders.filter(order_date__date__lte=to_date)
        except ValueError:
            pass
    
    # Search by customer name or order ID
    search_query = request.GET.get('search', '').strip()
    if search_query:
        orders = orders.filter(
            Q(customer__username__icontains=search_query) |
            Q(customer__first_name__icontains=search_query) |
            Q(customer__last_name__icontains=search_query) |
            Q(id__icontains=search_query)
        )
    
    # Sort orders
    sort_by = request.GET.get('sort', '-order_date')
    valid_sort_fields = [
        'order_date', '-order_date', 'status', '-status', 
        'total_amount', '-total_amount', 'customer__username', 
        '-customer__username', 'product__name', '-product__name'
    ]
    if sort_by in valid_sort_fields:
        orders = orders.order_by(sort_by)
    else:
        orders = orders.order_by('-order_date')
    
    # Calculate summary statistics before pagination using aggregate
    summary_stats = orders.aggregate(
        total_orders=Count('id'),
        pending_count=Count('id', filter=Q(status='pending')),
        out_for_delivery_count=Count('id', filter=Q(status='out_for_delivery')),
        delivered_count=Count('id', filter=Q(status='delivered'))
    )
    
    # Add pagination for better performance
    paginator = Paginator(orders, 25)  # Show 25 orders per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Get filter choices for template
    status_choices = Order.STATUS_CHOICES
    delivery_choices = Order.DELIVERY_CHOICES
    
    context = {
        'orders': page_obj,
        'status_choices': status_choices,
        'delivery_choices': delivery_choices,
        'current_filters': {
            'status': status_filter,
            'delivery_type': delivery_filter,
            'date_from': date_from,
            'date_to': date_to,
            'search': search_query,
            'sort': sort_by,
        },
        'summary_stats': summary_stats,
        'is_paginated': page_obj.has_other_pages(),
        'page_obj': page_obj,
    }
    
    return render(request, 'dealer/order_management.html', context)


@user_passes_test(is_dealer, login_url='core:login')
@require_http_methods(["POST"])
@csrf_protect
def update_order_status(request, order_id):
    """
    Update order status with HTMX support
    Requirements: 5.2, 5.3 - Order status updates without page refresh
    """
    order = get_object_or_404(Order, id=order_id)
    new_status = request.POST.get('status')
    
    if new_status not in dict(Order.STATUS_CHOICES):
        if request.headers.get('HX-Request'):
            return JsonResponse({
                'success': False,
                'message': 'Invalid status provided.'
            }, status=400)
        messages.error(request, 'Invalid status provided.')
        return redirect('core:order_management')
    
    # Validate status transition
    valid_transitions = {
        'pending': ['out_for_delivery', 'cancelled'],
        'out_for_delivery': ['delivered', 'cancelled'],
        'delivered': [],  # Cannot change from delivered
        'cancelled': []   # Cannot change from cancelled
    }
    
    if new_status not in valid_transitions.get(order.status, []):
        error_msg = f'Cannot change status from {order.get_status_display()} to {dict(Order.STATUS_CHOICES)[new_status]}.'
        if request.headers.get('HX-Request'):
            return JsonResponse({
                'success': False,
                'message': error_msg
            }, status=400)
        messages.error(request, error_msg)
        return redirect('core:order_management')
    
    # Update order status
    old_status = order.status
    order.status = new_status
    
    # Set delivery date if order is being marked as delivered
    if new_status == 'delivered' and not order.delivery_date:
        order.delivery_date = timezone.now()
    
    order.save()
    
    success_msg = f'Order #{order.id} status updated from {dict(Order.STATUS_CHOICES)[old_status]} to {order.get_status_display()}.'
    
    if request.headers.get('HX-Request'):
        # Return updated order row for HTMX
        context = {
            'order': order,
            'show_success_message': True,
            'success_message': success_msg
        }
        return render(request, 'dealer/order_row_partial.html', context)
    
    messages.success(request, success_msg)
    return redirect('core:order_management')


# Lazy loading views for performance optimization
@user_passes_test(is_dealer, login_url='core:login')
def lazy_load_orders(request):
    """
    Lazy load orders for infinite scroll or pagination
    Requirements: 9.1, 9.2 - Performance optimization with lazy loading
    """
    from django.core.paginator import Paginator
    
    page = int(request.GET.get('page', 1))
    page_size = int(request.GET.get('page_size', 25))
    
    # Apply same filters as order_management
    orders = Order.objects.select_related('customer', 'product').all()
    
    # Apply filters
    status_filter = request.GET.get('status', '')
    if status_filter and status_filter in dict(Order.STATUS_CHOICES):
        orders = orders.filter(status=status_filter)
    
    delivery_filter = request.GET.get('delivery_type', '')
    if delivery_filter and delivery_filter in dict(Order.DELIVERY_CHOICES):
        orders = orders.filter(delivery_type=delivery_filter)
    
    search_query = request.GET.get('search', '').strip()
    if search_query:
        orders = orders.filter(
            Q(customer__username__icontains=search_query) |
            Q(customer__first_name__icontains=search_query) |
            Q(customer__last_name__icontains=search_query) |
            Q(id__icontains=search_query)
        )
    
    sort_by = request.GET.get('sort', '-order_date')
    valid_sort_fields = [
        'order_date', '-order_date', 'status', '-status', 
        'total_amount', '-total_amount', 'customer__username', 
        '-customer__username', 'product__name', '-product__name'
    ]
    if sort_by in valid_sort_fields:
        orders = orders.order_by(sort_by)
    
    paginator = Paginator(orders, page_size)
    page_obj = paginator.get_page(page)
    
    context = {
        'orders': page_obj,
        'has_next': page_obj.has_next(),
        'next_page_number': page_obj.next_page_number() if page_obj.has_next() else None,
    }
    
    return render(request, 'dealer/order_rows_partial.html', context)


@login_required
def lazy_load_customer_orders(request):
    """
    Lazy load customer orders for infinite scroll
    Requirements: 9.1, 9.2 - Performance optimization with lazy loading
    """
    from django.core.paginator import Paginator
    
    page = int(request.GET.get('page', 1))
    page_size = int(request.GET.get('page_size', 20))
    
    orders = Order.objects.filter(customer=request.user).select_related('product').order_by('-order_date')
    
    # Apply filters
    status_filter = request.GET.get('status')
    if status_filter and status_filter in dict(Order.STATUS_CHOICES):
        orders = orders.filter(status=status_filter)
    
    sort_by = request.GET.get('sort', '-order_date')
    if sort_by in ['-order_date', 'order_date', '-total_amount', 'total_amount', 'status']:
        orders = orders.order_by(sort_by)
    
    paginator = Paginator(orders, page_size)
    page_obj = paginator.get_page(page)
    
    context = {
        'orders': page_obj,
        'has_next': page_obj.has_next(),
        'next_page_number': page_obj.next_page_number() if page_obj.has_next() else None,
    }
    
    return render(request, 'customer/order_rows_partial.html', context)


@user_passes_test(is_dealer, login_url='core:login')
def refresh_order_table(request):
    """
    HTMX endpoint for refreshing order table with current filters
    Requirements: 5.3 - Real-time updates without page refresh
    """
    # Reuse the same logic as order_management but return only the table
    from django.core.paginator import Paginator
    
    orders = Order.objects.select_related('customer', 'product').all()
    
    # Apply all filters
    status_filter = request.GET.get('status', '')
    if status_filter and status_filter in dict(Order.STATUS_CHOICES):
        orders = orders.filter(status=status_filter)
    
    delivery_filter = request.GET.get('delivery_type', '')
    if delivery_filter and delivery_filter in dict(Order.DELIVERY_CHOICES):
        orders = orders.filter(delivery_type=delivery_filter)
    
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')
    if date_from:
        try:
            from_date = datetime.strptime(date_from, '%Y-%m-%d').date()
            orders = orders.filter(order_date__date__gte=from_date)
        except ValueError:
            pass
    if date_to:
        try:
            to_date = datetime.strptime(date_to, '%Y-%m-%d').date()
            orders = orders.filter(order_date__date__lte=to_date)
        except ValueError:
            pass
    
    search_query = request.GET.get('search', '').strip()
    if search_query:
        orders = orders.filter(
            Q(customer__username__icontains=search_query) |
            Q(customer__first_name__icontains=search_query) |
            Q(customer__last_name__icontains=search_query) |
            Q(id__icontains=search_query)
        )
    
    sort_by = request.GET.get('sort', '-order_date')
    valid_sort_fields = [
        'order_date', '-order_date', 'status', '-status', 
        'total_amount', '-total_amount', 'customer__username', 
        '-customer__username', 'product__name', '-product__name'
    ]
    if sort_by in valid_sort_fields:
        orders = orders.order_by(sort_by)
    
    # Add pagination
    paginator = Paginator(orders, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'orders': page_obj,
        'is_paginated': page_obj.has_other_pages(),
        'page_obj': page_obj,
    }
    
    return render(request, 'dealer/order_table_partial.html', context)


@user_passes_test(is_dealer, login_url='core:login')
def order_detail_modal(request, order_id):
    """
    Order detail modal with customer information and delivery details
    Requirements: 5.4 - Order detail modal with customer information
    """
    order = get_object_or_404(Order, id=order_id)
    
    # Get customer profile if available
    customer_profile = None
    if hasattr(order.customer, 'customer_profile'):
        customer_profile = order.customer.customer_profile
    
    context = {
        'order': order,
        'customer_profile': customer_profile,
    }
    
    if request.headers.get('HX-Request'):
        return render(request, 'dealer/order_detail_modal.html', context)
    
    # Fallback for non-HTMX requests
    return render(request, 'dealer/order_detail.html', context)


@user_passes_test(is_dealer, login_url='core:login')
@require_http_methods(["POST"])
@csrf_protect
def bulk_order_operations(request):
    """
    Handle bulk operations on orders
    Requirements: 5.5 - Bulk order operations
    """
    operation = request.POST.get('operation')
    order_ids = request.POST.getlist('order_ids')
    
    if not order_ids:
        if request.headers.get('HX-Request'):
            return JsonResponse({
                'success': False,
                'message': 'No orders selected.'
            }, status=400)
        messages.error(request, 'No orders selected.')
        return redirect('core:order_management')
    
    try:
        order_ids = [int(id) for id in order_ids]
        orders = Order.objects.filter(id__in=order_ids)
        
        if not orders.exists():
            raise ValueError("No valid orders found.")
        
        success_count = 0
        error_messages = []
        
        if operation == 'mark_out_for_delivery':
            for order in orders:
                if order.status == 'pending':
                    order.status = 'out_for_delivery'
                    order.save()
                    success_count += 1
                else:
                    error_messages.append(f'Order #{order.id} cannot be marked as out for delivery (current status: {order.get_status_display()})')
        
        elif operation == 'mark_delivered':
            for order in orders:
                if order.status == 'out_for_delivery':
                    order.status = 'delivered'
                    if not order.delivery_date:
                        order.delivery_date = timezone.now()
                    order.save()
                    success_count += 1
                else:
                    error_messages.append(f'Order #{order.id} cannot be marked as delivered (current status: {order.get_status_display()})')
        
        elif operation == 'cancel_orders':
            for order in orders:
                if order.status in ['pending', 'out_for_delivery']:
                    order.status = 'cancelled'
                    order.save()
                    success_count += 1
                else:
                    error_messages.append(f'Order #{order.id} cannot be cancelled (current status: {order.get_status_display()})')
        
        else:
            raise ValueError("Invalid operation.")
        
        # Prepare response message
        if success_count > 0:
            success_msg = f'Successfully processed {success_count} order(s).'
            if error_messages:
                success_msg += f' {len(error_messages)} order(s) could not be processed.'
        else:
            success_msg = 'No orders were processed.'
        
        if request.headers.get('HX-Request'):
            # Refresh the order table
            return JsonResponse({
                'success': True,
                'message': success_msg,
                'refresh_table': True,
                'errors': error_messages
            })
        
        messages.success(request, success_msg)
        for error in error_messages:
            messages.warning(request, error)
    
    except (ValueError, TypeError) as e:
        error_msg = f'Error processing bulk operation: {str(e)}'
        if request.headers.get('HX-Request'):
            return JsonResponse({
                'success': False,
                'message': error_msg
            }, status=400)
        messages.error(request, error_msg)
    
    return redirect('core:order_management')


@user_passes_test(is_dealer, login_url='core:login')
def refresh_order_table(request):
    """
    HTMX endpoint for refreshing the order management table
    Requirements: 5.3 - HTMX-powered updates without page refresh
    """
    if request.headers.get('HX-Request'):
        # Use the same filtering logic as order_management view
        orders = Order.objects.select_related('customer', 'product').all()
        
        # Apply filters (same logic as order_management)
        status_filter = request.GET.get('status', '')
        if status_filter and status_filter in dict(Order.STATUS_CHOICES):
            orders = orders.filter(status=status_filter)
        
        delivery_filter = request.GET.get('delivery_type', '')
        if delivery_filter and delivery_filter in dict(Order.DELIVERY_CHOICES):
            orders = orders.filter(delivery_type=delivery_filter)
        
        date_from = request.GET.get('date_from', '')
        date_to = request.GET.get('date_to', '')
        if date_from:
            try:
                from_date = datetime.strptime(date_from, '%Y-%m-%d').date()
                orders = orders.filter(order_date__date__gte=from_date)
            except ValueError:
                pass
        if date_to:
            try:
                to_date = datetime.strptime(date_to, '%Y-%m-%d').date()
                orders = orders.filter(order_date__date__lte=to_date)
            except ValueError:
                pass
        
        search_query = request.GET.get('search', '').strip()
        if search_query:
            orders = orders.filter(
                Q(customer__username__icontains=search_query) |
                Q(customer__first_name__icontains=search_query) |
                Q(customer__last_name__icontains=search_query) |
                Q(id__icontains=search_query)
            )
        
        sort_by = request.GET.get('sort', '-order_date')
        valid_sort_fields = [
            'order_date', '-order_date', 'status', '-status', 
            'total_amount', '-total_amount', 'customer__username', 
            '-customer__username', 'product__name', '-product__name'
        ]
        if sort_by in valid_sort_fields:
            orders = orders.order_by(sort_by)
        else:
            orders = orders.order_by('-order_date')
        
        context = {
            'orders': orders,
            'current_filters': {
                'status': status_filter,
                'delivery_type': delivery_filter,
                'date_from': date_from,
                'date_to': date_to,
                'search': search_query,
                'sort': sort_by,
            }
        }
        
        return render(request, 'dealer/order_table_partial.html', context)
    
    return redirect('core:order_management')


@login_required
def refresh_dashboard_orders(request):
    """
    HTMX endpoint for refreshing dashboard order statistics
    Requirements: 3.2 - Real-time updates for dashboard
    """
    if request.headers.get('HX-Request'):
        recent_orders = Order.objects.filter(customer=request.user).select_related('product').order_by('-order_date')[:5]
        
        # Calculate order counts
        total_orders = request.user.orders.count()
        pending_orders = request.user.orders.filter(status='pending').count()
        delivered_orders = request.user.orders.filter(status='delivered').count()
        
        context = {
            'recent_orders': recent_orders,
            'total_orders': total_orders,
            'pending_orders': pending_orders,
            'delivered_orders': delivered_orders,
        }
        return render(request, 'customer/dashboard_orders_partial.html', context)
    
    return redirect('core:customer_dashboard')


# Inventory Management Views
@user_passes_test(is_dealer, login_url='core:login')
def inventory_management(request):
    """
    Inventory dashboard showing current stock levels and low stock warnings
    Requirements: 6.1, 6.3 - Inventory dashboard with stock levels and warnings
    """
    # Get all products with stock information
    products = LPGProduct.objects.filter(is_active=True).order_by('name', 'size')
    
    # Get low stock products
    low_stock_products = [product for product in products if product.is_low_stock]
    
    # Get recent delivery logs for stock movement history
    recent_deliveries = DeliveryLog.objects.select_related('product', 'logged_by').order_by('-delivery_date')[:10]
    
    # Calculate inventory statistics
    total_products = products.count()
    low_stock_count = len(low_stock_products)
    total_stock_value = sum(product.current_stock * product.price for product in products)
    
    # Get stock movement data for the last 30 days
    thirty_days_ago = timezone.now() - timedelta(days=30)
    recent_stock_movements = DeliveryLog.objects.filter(
        delivery_date__gte=thirty_days_ago
    ).select_related('product', 'logged_by').order_by('-delivery_date')
    
    context = {
        'products': products,
        'low_stock_products': low_stock_products,
        'recent_deliveries': recent_deliveries,
        'recent_stock_movements': recent_stock_movements,
        'inventory_stats': {
            'total_products': total_products,
            'low_stock_count': low_stock_count,
            'total_stock_value': total_stock_value,
        }
    }
    
    return render(request, 'dealer/inventory.html', context)


@user_passes_test(is_dealer, login_url='core:login')
@require_http_methods(["POST"])
@csrf_protect
def log_delivery(request):
    """
    Log new distributor delivery with automatic inventory adjustment
    Requirements: 6.2, 6.4 - Delivery logging with automatic stock updates
    """
    if request.method == 'POST':
        form = DeliveryLogForm(request.POST)
        if form.is_valid():
            try:
                with transaction.atomic():
                    delivery_log = form.save(commit=False)
                    delivery_log.logged_by = request.user
                    
                    # Calculate total cost if not provided
                    if not delivery_log.total_cost:
                        delivery_log.total_cost = delivery_log.cost_per_unit * delivery_log.quantity_received
                    
                    # Save delivery log (this will automatically update product stock via model save method)
                    delivery_log.save()
                    
                    success_msg = f'Successfully logged delivery of {delivery_log.quantity_received}x {delivery_log.product.name} from {delivery_log.supplier}.'
                    
                    if request.headers.get('HX-Request'):
                        # Return success message and close modal
                        context = {
                            'success': True,
                            'message': success_msg,
                            'delivery_log': delivery_log
                        }
                        return render(request, 'dealer/delivery_success_partial.html', context)
                    
                    messages.success(request, success_msg)
                    return redirect('core:inventory_management')
                    
            except Exception as e:
                error_msg = f'Error logging delivery: {str(e)}'
                if request.headers.get('HX-Request'):
                    return JsonResponse({
                        'success': False,
                        'message': error_msg
                    }, status=400)
                messages.error(request, error_msg)
        else:
            # Form validation errors
            if request.headers.get('HX-Request'):
                errors = []
                for field, field_errors in form.errors.items():
                    for error in field_errors:
                        errors.append(f'{field}: {error}')
                return JsonResponse({
                    'success': False,
                    'message': 'Form validation failed.',
                    'errors': errors
                }, status=400)
            
            # Add form errors to messages for non-HTMX requests
            for field, field_errors in form.errors.items():
                for error in field_errors:
                    messages.error(request, f'{field}: {error}')
    
    return redirect('core:inventory_management')


@user_passes_test(is_dealer, login_url='core:login')
def refresh_inventory_dashboard(request):
    """
    HTMX/Unpoly endpoint for refreshing inventory dashboard
    Requirements: 6.5 - Real-time inventory displays with Unpoly updates
    """
    if request.headers.get('HX-Request') or request.headers.get('X-Up-Target'):
        # Get updated inventory data
        products = LPGProduct.objects.filter(is_active=True).order_by('name', 'size')
        low_stock_products = [product for product in products if product.is_low_stock]
        
        # Calculate updated statistics
        total_products = products.count()
        low_stock_count = len(low_stock_products)
        total_stock_value = sum(product.current_stock * product.price for product in products)
        
        context = {
            'products': products,
            'low_stock_products': low_stock_products,
            'inventory_stats': {
                'total_products': total_products,
                'low_stock_count': low_stock_count,
                'total_stock_value': total_stock_value,
            }
        }
        
        return render(request, 'dealer/inventory_dashboard_partial.html', context)
    
    return redirect('core:inventory_management')


@user_passes_test(is_dealer, login_url='core:login')
def refresh_stock_movements(request):
    """
    HTMX endpoint for refreshing stock movement history
    Requirements: 6.4 - Stock movement history and tracking
    """
    if request.headers.get('HX-Request'):
        # Get stock movement data for the last 30 days
        thirty_days_ago = timezone.now() - timedelta(days=30)
        recent_stock_movements = DeliveryLog.objects.filter(
            delivery_date__gte=thirty_days_ago
        ).select_related('product', 'logged_by').order_by('-delivery_date')
        
        context = {
            'recent_stock_movements': recent_stock_movements,
        }
        
        return render(request, 'dealer/stock_movements_partial.html', context)
    
    return redirect('core:inventory_management')


@user_passes_test(is_dealer, login_url='core:login')
def get_delivery_form(request):
    """
    HTMX endpoint to get delivery logging form modal
    Requirements: 6.2 - Delivery logging modal with Alpine.js
    """
    if request.headers.get('HX-Request'):
        form = DeliveryLogForm()
        products = LPGProduct.objects.filter(is_active=True).order_by('name', 'size')
        
        context = {
            'form': form,
            'products': products,
        }
        
        return render(request, 'dealer/delivery_form_modal.html', context)
    
    return redirect('core:inventory_management')

# Reporting System Views
@user_passes_test(is_dealer, login_url='core:login')
def reports_dashboard(request):
    """
    Main reports dashboard with report options and quick stats
    Requirements: 7.1, 7.2, 7.3, 7.4, 7.5 - Report generation system
    """
    # Calculate quick statistics for the dashboard
    today = timezone.now().date()
    current_month = today.replace(day=1)
    last_month = (current_month - timedelta(days=1)).replace(day=1)
    
    # Monthly statistics
    current_month_orders = Order.objects.filter(
        order_date__date__gte=current_month,
        status='delivered'
    ).count()
    
    current_month_revenue = Order.objects.filter(
        order_date__date__gte=current_month,
        status='delivered'
    ).aggregate(total=Sum('total_amount'))['total'] or 0
    
    last_month_orders = Order.objects.filter(
        order_date__date__gte=last_month,
        order_date__date__lt=current_month,
        status='delivered'
    ).count()
    
    last_month_revenue = Order.objects.filter(
        order_date__date__gte=last_month,
        order_date__date__lt=current_month,
        status='delivered'
    ).aggregate(total=Sum('total_amount'))['total'] or 0
    
    # Inventory statistics
    total_stock_value = LPGProduct.objects.filter(is_active=True).aggregate(
        total=Sum(F('current_stock') * F('price'))
    )['total'] or 0
    
    low_stock_count = LPGProduct.objects.filter(
        is_active=True,
        current_stock__lte=F('minimum_stock')
    ).count()
    
    # Recent deliveries value
    thirty_days_ago = timezone.now() - timedelta(days=30)
    recent_deliveries_value = DeliveryLog.objects.filter(
        delivery_date__gte=thirty_days_ago
    ).aggregate(total=Sum('total_cost'))['total'] or 0
    
    context = {
        'quick_stats': {
            'current_month_orders': current_month_orders,
            'current_month_revenue': current_month_revenue,
            'last_month_orders': last_month_orders,
            'last_month_revenue': last_month_revenue,
            'total_stock_value': total_stock_value,
            'low_stock_count': low_stock_count,
            'recent_deliveries_value': recent_deliveries_value,
        },
        'products': LPGProduct.objects.filter(is_active=True).order_by('name', 'size'),
        'customers': User.objects.filter(orders__isnull=False).distinct().order_by('username'),
    }
    
    return render(request, 'dealer/reports_dashboard.html', context)


@user_passes_test(is_dealer, login_url='core:login')
def sales_report(request):
    """
    Generate sales report with date range filtering
    Requirements: 7.1, 7.3, 7.4 - Sales report generation with filtering
    """
    # Get filter parameters
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')
    product_filter = request.GET.get('product', '')
    customer_filter = request.GET.get('customer', '')
    
    # Default to last 30 days if no dates provided
    if not date_from or not date_to:
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=30)
        if not date_from:
            date_from = start_date.strftime('%Y-%m-%d')
        if not date_to:
            date_to = end_date.strftime('%Y-%m-%d')
    
    # Build query for delivered orders
    orders = Order.objects.filter(status='delivered').select_related('customer', 'product')
    
    # Apply date filters
    try:
        from_date = datetime.strptime(date_from, '%Y-%m-%d').date()
        to_date = datetime.strptime(date_to, '%Y-%m-%d').date()
        orders = orders.filter(
            delivery_date__date__gte=from_date,
            delivery_date__date__lte=to_date
        )
    except ValueError:
        # Invalid date format, use default
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=30)
        orders = orders.filter(
            delivery_date__date__gte=start_date,
            delivery_date__date__lte=end_date
        )
        date_from = start_date.strftime('%Y-%m-%d')
        date_to = end_date.strftime('%Y-%m-%d')
    
    # Apply product filter
    if product_filter:
        try:
            product_id = int(product_filter)
            orders = orders.filter(product_id=product_id)
        except (ValueError, TypeError):
            pass
    
    # Apply customer filter
    if customer_filter:
        try:
            customer_id = int(customer_filter)
            orders = orders.filter(customer_id=customer_id)
        except (ValueError, TypeError):
            pass
    
    # Calculate summary statistics
    total_orders = orders.count()
    total_revenue = orders.aggregate(total=Sum('total_amount'))['total'] or 0
    total_quantity = orders.aggregate(total=Sum('quantity'))['total'] or 0
    average_order_value = orders.aggregate(avg=Avg('total_amount'))['avg'] or 0
    
    # Product breakdown
    product_stats = orders.values(
        'product__name', 'product__size'
    ).annotate(
        total_quantity=Sum('quantity'),
        total_revenue=Sum('total_amount'),
        order_count=Count('id')
    ).order_by('-total_revenue')
    
    # Customer breakdown
    customer_stats = orders.values(
        'customer__username', 'customer__first_name', 'customer__last_name'
    ).annotate(
        total_orders=Count('id'),
        total_spent=Sum('total_amount'),
        total_quantity=Sum('quantity')
    ).order_by('-total_spent')
    
    # Daily sales trend
    daily_sales = orders.extra(
        select={'day': 'DATE(delivery_date)'}
    ).values('day').annotate(
        daily_orders=Count('id'),
        daily_revenue=Sum('total_amount')
    ).order_by('day')
    
    context = {
        'report_type': 'sales',
        'orders': orders.order_by('-delivery_date'),
        'filters': {
            'date_from': date_from,
            'date_to': date_to,
            'product': product_filter,
            'customer': customer_filter,
        },
        'summary': {
            'total_orders': total_orders,
            'total_revenue': total_revenue,
            'total_quantity': total_quantity,
            'average_order_value': average_order_value,
        },
        'product_stats': product_stats,
        'customer_stats': customer_stats,
        'daily_sales': daily_sales,
        'products': LPGProduct.objects.filter(is_active=True).order_by('name', 'size'),
        'customers': User.objects.filter(orders__isnull=False).distinct().order_by('username'),
    }
    
    return render(request, 'dealer/sales_report.html', context)


@user_passes_test(is_dealer, login_url='core:login')
def stock_report(request):
    """
    Generate stock report showing inventory levels and movement
    Requirements: 7.2, 7.3, 7.4 - Stock report with inventory levels and movement
    """
    # Get filter parameters
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')
    product_filter = request.GET.get('product', '')
    
    # Default to last 30 days if no dates provided
    if not date_from or not date_to:
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=30)
        if not date_from:
            date_from = start_date.strftime('%Y-%m-%d')
        if not date_to:
            date_to = end_date.strftime('%Y-%m-%d')
    
    # Get all active products
    products = LPGProduct.objects.filter(is_active=True).order_by('name', 'size')
    
    # Apply product filter
    if product_filter:
        try:
            product_id = int(product_filter)
            products = products.filter(id=product_id)
        except (ValueError, TypeError):
            pass
    
    # Get delivery logs for the period
    try:
        from_date = datetime.strptime(date_from, '%Y-%m-%d').date()
        to_date = datetime.strptime(date_to, '%Y-%m-%d').date()
        deliveries = DeliveryLog.objects.filter(
            delivery_date__date__gte=from_date,
            delivery_date__date__lte=to_date
        ).select_related('product', 'logged_by')
    except ValueError:
        # Invalid date format, use default
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=30)
        deliveries = DeliveryLog.objects.filter(
            delivery_date__date__gte=start_date,
            delivery_date__date__lte=end_date
        ).select_related('product', 'logged_by')
        date_from = start_date.strftime('%Y-%m-%d')
        date_to = end_date.strftime('%Y-%m-%d')
    
    # Apply product filter to deliveries
    if product_filter:
        try:
            product_id = int(product_filter)
            deliveries = deliveries.filter(product_id=product_id)
        except (ValueError, TypeError):
            pass
    
    # Get sales for the period (delivered orders)
    try:
        from_date = datetime.strptime(date_from, '%Y-%m-%d').date()
        to_date = datetime.strptime(date_to, '%Y-%m-%d').date()
        sales = Order.objects.filter(
            status='delivered',
            delivery_date__date__gte=from_date,
            delivery_date__date__lte=to_date
        ).select_related('product', 'customer')
    except ValueError:
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=30)
        sales = Order.objects.filter(
            status='delivered',
            delivery_date__date__gte=start_date,
            delivery_date__date__lte=end_date
        ).select_related('product', 'customer')
    
    # Apply product filter to sales
    if product_filter:
        try:
            product_id = int(product_filter)
            sales = sales.filter(product_id=product_id)
        except (ValueError, TypeError):
            pass
    
    # Calculate inventory statistics
    total_stock_value = products.aggregate(
        total=Sum(F('current_stock') * F('price'))
    )['total'] or 0
    
    total_current_stock = products.aggregate(
        total=Sum('current_stock')
    )['total'] or 0
    
    low_stock_products = products.filter(
        current_stock__lte=F('minimum_stock')
    ).count()
    
    out_of_stock_products = products.filter(current_stock=0).count()
    
    # Delivery statistics
    total_deliveries = deliveries.count()
    total_delivered_quantity = deliveries.aggregate(
        total=Sum('quantity_received')
    )['total'] or 0
    total_delivery_cost = deliveries.aggregate(
        total=Sum('total_cost')
    )['total'] or 0
    
    # Sales statistics
    total_sales = sales.count()
    total_sold_quantity = sales.aggregate(
        total=Sum('quantity')
    )['total'] or 0
    total_sales_revenue = sales.aggregate(
        total=Sum('total_amount')
    )['total'] or 0
    
    # Product-wise inventory details
    product_details = []
    for product in products:
        # Get deliveries for this product in the period
        product_deliveries = deliveries.filter(product=product)
        delivered_qty = product_deliveries.aggregate(
            total=Sum('quantity_received')
        )['total'] or 0
        delivery_cost = product_deliveries.aggregate(
            total=Sum('total_cost')
        )['total'] or 0
        
        # Get sales for this product in the period
        product_sales = sales.filter(product=product)
        sold_qty = product_sales.aggregate(
            total=Sum('quantity')
        )['total'] or 0
        sales_revenue = product_sales.aggregate(
            total=Sum('total_amount')
        )['total'] or 0
        
        # Calculate stock movement
        net_movement = delivered_qty - sold_qty
        stock_value = product.current_stock * product.price
        
        product_details.append({
            'product': product,
            'current_stock': product.current_stock,
            'minimum_stock': product.minimum_stock,
            'stock_value': stock_value,
            'delivered_qty': delivered_qty,
            'sold_qty': sold_qty,
            'net_movement': net_movement,
            'delivery_cost': delivery_cost,
            'sales_revenue': sales_revenue,
            'is_low_stock': product.is_low_stock,
            'is_out_of_stock': product.current_stock == 0,
        })
    
    # Recent stock movements
    recent_movements = deliveries.order_by('-delivery_date')[:20]
    
    context = {
        'report_type': 'stock',
        'filters': {
            'date_from': date_from,
            'date_to': date_to,
            'product': product_filter,
        },
        'inventory_summary': {
            'total_stock_value': total_stock_value,
            'total_current_stock': total_current_stock,
            'low_stock_products': low_stock_products,
            'out_of_stock_products': out_of_stock_products,
        },
        'period_summary': {
            'total_deliveries': total_deliveries,
            'total_delivered_quantity': total_delivered_quantity,
            'total_delivery_cost': total_delivery_cost,
            'total_sales': total_sales,
            'total_sold_quantity': total_sold_quantity,
            'total_sales_revenue': total_sales_revenue,
        },
        'product_details': product_details,
        'recent_movements': recent_movements,
        'products': LPGProduct.objects.filter(is_active=True).order_by('name', 'size'),
    }
    
    return render(request, 'dealer/stock_report.html', context)


@user_passes_test(is_dealer, login_url='core:login')
def print_report(request):
    """
    Generate printable version of reports
    Requirements: 7.5 - Mobile-responsive and printable reports
    """
    report_type = request.GET.get('type', 'sales')
    
    if report_type == 'sales':
        # Redirect to sales report with print parameter
        query_params = request.GET.copy()
        query_params['print'] = '1'
        return redirect(f"{request.build_absolute_uri('/dealer/reports/sales/')}?{query_params.urlencode()}")
    elif report_type == 'stock':
        # Redirect to stock report with print parameter
        query_params = request.GET.copy()
        query_params['print'] = '1'
        return redirect(f"{request.build_absolute_uri('/dealer/reports/stock/')}?{query_params.urlencode()}")
    
    return redirect('core:reports_dashboard')