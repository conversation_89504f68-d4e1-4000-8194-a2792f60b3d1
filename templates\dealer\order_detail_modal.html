<!-- Order Detail Modal -->
<div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" 
     x-data="{ show: true }" 
     x-show="show" 
     x-transition:enter="ease-out duration-300"
     x-transition:enter-start="opacity-0"
     x-transition:enter-end="opacity-100"
     x-transition:leave="ease-in duration-200"
     x-transition:leave-start="opacity-100"
     x-transition:leave-end="opacity-0"
     @click.self="show = false; setTimeout(() => document.getElementById('order-detail-modal').innerHTML = '', 300)">
    
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white"
         x-transition:enter="ease-out duration-300"
         x-transition:enter-start="opacity-0 transform scale-95"
         x-transition:enter-end="opacity-100 transform scale-100"
         x-transition:leave="ease-in duration-200"
         x-transition:leave-start="opacity-100 transform scale-100"
         x-transition:leave-end="opacity-0 transform scale-95">
        
        <!-- Modal Header -->
        <div class="flex items-center justify-between pb-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Order Details #{{ order.id }}</h3>
            <button @click="show = false; setTimeout(() => document.getElementById('order-detail-modal').innerHTML = '', 300)"
                    class="text-gray-400 hover:text-gray-600">
                <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                </svg>
            </button>
        </div>

        <!-- Modal Content -->
        <div class="mt-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Order Information -->
                <div class="space-y-4">
                    <h4 class="text-md font-semibold text-gray-900 border-b pb-2">Order Information</h4>
                    
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-sm font-medium text-gray-500">Order ID:</span>
                            <span class="text-sm text-gray-900">#{{ order.id }}</span>
                        </div>
                        
                        <div class="flex justify-between">
                            <span class="text-sm font-medium text-gray-500">Status:</span>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                         {% if order.status == 'pending' %}bg-yellow-100 text-yellow-800
                                         {% elif order.status == 'out_for_delivery' %}bg-orange-100 text-orange-800
                                         {% elif order.status == 'delivered' %}bg-green-100 text-green-800
                                         {% elif order.status == 'cancelled' %}bg-red-100 text-red-800
                                         {% endif %}">
                                {{ order.get_status_display }}
                            </span>
                        </div>
                        
                        <div class="flex justify-between">
                            <span class="text-sm font-medium text-gray-500">Order Date:</span>
                            <span class="text-sm text-gray-900">{{ order.order_date|date:"M d, Y g:i A" }}</span>
                        </div>
                        
                        {% if order.delivery_date %}
                        <div class="flex justify-between">
                            <span class="text-sm font-medium text-gray-500">Delivery Date:</span>
                            <span class="text-sm text-gray-900">{{ order.delivery_date|date:"M d, Y g:i A" }}</span>
                        </div>
                        {% endif %}
                        
                        <div class="flex justify-between">
                            <span class="text-sm font-medium text-gray-500">Delivery Type:</span>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                         {% if order.delivery_type == 'delivery' %}bg-blue-100 text-blue-800{% else %}bg-gray-100 text-gray-800{% endif %}">
                                {{ order.get_delivery_type_display }}
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Customer Information -->
                <div class="space-y-4">
                    <h4 class="text-md font-semibold text-gray-900 border-b pb-2">Customer Information</h4>
                    
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-sm font-medium text-gray-500">Username:</span>
                            <span class="text-sm text-gray-900">{{ order.customer.username }}</span>
                        </div>
                        
                        {% if order.customer.first_name or order.customer.last_name %}
                        <div class="flex justify-between">
                            <span class="text-sm font-medium text-gray-500">Name:</span>
                            <span class="text-sm text-gray-900">{{ order.customer.first_name }} {{ order.customer.last_name }}</span>
                        </div>
                        {% endif %}
                        
                        {% if order.customer.email %}
                        <div class="flex justify-between">
                            <span class="text-sm font-medium text-gray-500">Email:</span>
                            <span class="text-sm text-gray-900">{{ order.customer.email }}</span>
                        </div>
                        {% endif %}
                        
                        {% if customer_profile %}
                        <div class="flex justify-between">
                            <span class="text-sm font-medium text-gray-500">Phone:</span>
                            <span class="text-sm text-gray-900">{{ customer_profile.phone_number }}</span>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Product Information -->
            <div class="mt-6 space-y-4">
                <h4 class="text-md font-semibold text-gray-900 border-b pb-2">Product Information</h4>
                
                <div class="bg-gray-50 rounded-lg p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <h5 class="text-sm font-medium text-gray-900">{{ order.product.name }}</h5>
                            <p class="text-sm text-gray-500">{{ order.product.size }}</p>
                        </div>
                        <div class="text-right">
                            <p class="text-sm font-medium text-gray-900">₱{{ order.product.price|floatformat:2 }} each</p>
                            <p class="text-sm text-gray-500">Quantity: {{ order.quantity }}</p>
                        </div>
                    </div>
                    <div class="mt-3 pt-3 border-t border-gray-200">
                        <div class="flex justify-between">
                            <span class="text-base font-medium text-gray-900">Total Amount:</span>
                            <span class="text-base font-bold text-prycegas-orange">₱{{ order.total_amount|floatformat:2 }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Delivery Information -->
            <div class="mt-6 space-y-4">
                <h4 class="text-md font-semibold text-gray-900 border-b pb-2">Delivery Information</h4>
                
                <div class="space-y-3">
                    <div>
                        <span class="text-sm font-medium text-gray-500">Delivery Address:</span>
                        <p class="text-sm text-gray-900 mt-1">{{ order.delivery_address }}</p>
                    </div>
                    
                    {% if customer_profile.delivery_instructions %}
                    <div>
                        <span class="text-sm font-medium text-gray-500">Delivery Instructions:</span>
                        <p class="text-sm text-gray-900 mt-1">{{ customer_profile.delivery_instructions }}</p>
                    </div>
                    {% endif %}
                    
                    {% if order.notes %}
                    <div>
                        <span class="text-sm font-medium text-gray-500">Order Notes:</span>
                        <p class="text-sm text-gray-900 mt-1">{{ order.notes }}</p>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Status Update Actions -->
            {% if order.status != 'delivered' and order.status != 'cancelled' %}
            <div class="mt-6 pt-4 border-t border-gray-200">
                <h4 class="text-md font-semibold text-gray-900 mb-3">Update Status</h4>
                <div class="flex space-x-3">
                    {% if order.status == 'pending' %}
                        <button hx-post="{% url 'core:update_order_status' order.id %}"
                                hx-vals='{"status": "out_for_delivery"}'
                                hx-target="#order-row-{{ order.id }}"
                                hx-swap="outerHTML"
                                @click="show = false; setTimeout(() => document.getElementById('order-detail-modal').innerHTML = '', 300)"
                                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-prycegas-orange hover:bg-prycegas-orange-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-prycegas-orange">
                            Mark as Out for Delivery
                        </button>
                        <button hx-post="{% url 'core:update_order_status' order.id %}"
                                hx-vals='{"status": "cancelled"}'
                                hx-target="#order-row-{{ order.id }}"
                                hx-swap="outerHTML"
                                @click="show = false; setTimeout(() => document.getElementById('order-detail-modal').innerHTML = '', 300)"
                                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                            Cancel Order
                        </button>
                    {% elif order.status == 'out_for_delivery' %}
                        <button hx-post="{% url 'core:update_order_status' order.id %}"
                                hx-vals='{"status": "delivered"}'
                                hx-target="#order-row-{{ order.id }}"
                                hx-swap="outerHTML"
                                @click="show = false; setTimeout(() => document.getElementById('order-detail-modal').innerHTML = '', 300)"
                                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                            Mark as Delivered
                        </button>
                        <button hx-post="{% url 'core:update_order_status' order.id %}"
                                hx-vals='{"status": "cancelled"}'
                                hx-target="#order-row-{{ order.id }}"
                                hx-swap="outerHTML"
                                @click="show = false; setTimeout(() => document.getElementById('order-detail-modal').innerHTML = '', 300)"
                                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                            Cancel Order
                        </button>
                    {% endif %}
                </div>
            </div>
            {% endif %}
        </div>

        <!-- Modal Footer -->
        <div class="mt-6 pt-4 border-t border-gray-200 flex justify-end">
            <button @click="show = false; setTimeout(() => document.getElementById('order-detail-modal').innerHTML = '', 300)"
                    class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-prycegas-orange">
                Close
            </button>
        </div>
    </div>
</div>