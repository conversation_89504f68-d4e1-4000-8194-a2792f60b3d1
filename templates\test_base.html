{% extends 'base.html' %}

{% block title %}Test - Prycegas Station{% endblock %}

{% block content %}
<div class="bg-white shadow rounded-lg p-6">
    <h1 class="text-2xl font-bold text-gray-900 mb-4">Base Template Test</h1>
    
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <!-- Color Scheme Test -->
        <div class="bg-white p-4 rounded-lg border">
            <h2 class="text-lg font-semibold mb-3">Color Scheme</h2>
            <div class="space-y-2">
                <div class="flex items-center space-x-2">
                    <div class="w-4 h-4 bg-prycegas-black rounded"></div>
                    <span class="text-sm">Prycegas Black</span>
                </div>
                <div class="flex items-center space-x-2">
                    <div class="w-4 h-4 bg-prycegas-orange rounded"></div>
                    <span class="text-sm">Prycegas Orange</span>
                </div>
                <div class="flex items-center space-x-2">
                    <div class="w-4 h-4 bg-prycegas-orange-light rounded"></div>
                    <span class="text-sm">Orange Light</span>
                </div>
                <div class="flex items-center space-x-2">
                    <div class="w-4 h-4 bg-prycegas-gray rounded"></div>
                    <span class="text-sm">Prycegas Gray</span>
                </div>
            </div>
        </div>

        <!-- HTMX Test -->
        <div class="bg-white p-4 rounded-lg border">
            <h2 class="text-lg font-semibold mb-3">HTMX Test</h2>
            <button hx-get="/test-htmx/" 
                    hx-target="#htmx-result"
                    class="bg-prycegas-orange hover:bg-prycegas-orange-dark text-white px-4 py-2 rounded transition-colors">
                Test HTMX
            </button>
            <div id="htmx-result" class="mt-2 text-sm text-gray-600">
                Click button to test HTMX
            </div>
        </div>

        <!-- Alpine.js Test -->
        <div class="bg-white p-4 rounded-lg border" x-data="{ count: 0 }">
            <h2 class="text-lg font-semibold mb-3">Alpine.js Test</h2>
            <div class="space-y-2">
                <p class="text-sm">Count: <span x-text="count" class="font-bold"></span></p>
                <button @click="count++" 
                        class="bg-prycegas-orange hover:bg-prycegas-orange-dark text-white px-3 py-1 rounded text-sm transition-colors">
                    Increment
                </button>
                <button @click="count = 0" 
                        class="bg-gray-500 hover:bg-gray-600 text-white px-3 py-1 rounded text-sm transition-colors ml-2">
                    Reset
                </button>
            </div>
        </div>

        <!-- Toast Notification Test -->
        <div class="bg-white p-4 rounded-lg border">
            <h2 class="text-lg font-semibold mb-3">Toast Notifications</h2>
            <div class="space-y-2">
                <button onclick="showToast('success', 'Success!', 'This is a success message')"
                        class="bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded text-sm transition-colors block">
                    Success Toast
                </button>
                <button onclick="showToast('error', 'Error!', 'This is an error message')"
                        class="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded text-sm transition-colors block">
                    Error Toast
                </button>
                <button onclick="showToast('info', 'Info', 'This is an info message')"
                        class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm transition-colors block">
                    Info Toast
                </button>
            </div>
        </div>

        <!-- Responsive Test -->
        <div class="bg-white p-4 rounded-lg border">
            <h2 class="text-lg font-semibold mb-3">Responsive Design</h2>
            <div class="text-sm text-gray-600">
                <p class="block sm:hidden">Mobile View</p>
                <p class="hidden sm:block md:hidden">Tablet View</p>
                <p class="hidden md:block lg:hidden">Desktop View</p>
                <p class="hidden lg:block">Large Desktop View</p>
            </div>
        </div>

        <!-- Unpoly Test -->
        <div class="bg-white p-4 rounded-lg border">
            <h2 class="text-lg font-semibold mb-3">Unpoly Test</h2>
            <a href="#" up-follow 
               class="text-prycegas-orange hover:text-prycegas-orange-dark underline">
                Test Unpoly Link
            </a>
            <p class="text-xs text-gray-500 mt-1">
                Links with up-follow should load faster
            </p>
        </div>
    </div>
</div>

<script>
function showToast(type, title, message) {
    const toast = {
        id: Date.now(),
        type: type,
        title: title,
        message: message
    };
    window.dispatchEvent(new CustomEvent('toast', { detail: toast }));
}
</script>
{% endblock %}