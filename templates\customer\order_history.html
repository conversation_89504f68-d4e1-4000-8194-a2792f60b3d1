{% extends 'base.html' %}

{% block title %}Order History - Prycegas Station{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <div class="mb-4 sm:mb-0">
                    <h1 class="text-2xl font-bold text-gray-900">Order History</h1>
                    <p class="mt-1 text-sm text-gray-600">Track your LPG orders and delivery status</p>
                </div>
                <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
                    <a href="{% url 'core:place_order' %}" 
                       class="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-prycegas-orange hover:bg-prycegas-orange-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-prycegas-orange">
                        <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                        </svg>
                        New Order
                    </a>
                    <a href="{% url 'core:customer_dashboard' %}" 
                       class="inline-flex items-center justify-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-prycegas-orange">
                        ← Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter and Sort Options -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
                <div class="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-4">
                    <label for="status-filter" class="text-sm font-medium text-gray-700">Filter by status:</label>
                    <select id="status-filter" 
                            hx-get="{% url 'core:refresh_order_status' %}"
                            hx-target="#orders-container"
                            hx-include="[name='sort']"
                            name="status"
                            class="block w-full sm:w-48 px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-prycegas-orange focus:border-prycegas-orange text-sm">
                        <option value="">All Orders</option>
                        {% for status_value, status_label in status_choices %}
                            <option value="{{ status_value }}" {% if current_status == status_value %}selected{% endif %}>
                                {{ status_label }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-4">
                    <label for="sort-select" class="text-sm font-medium text-gray-700">Sort by:</label>
                    <select id="sort-select" 
                            hx-get="{% url 'core:refresh_order_status' %}"
                            hx-target="#orders-container"
                            hx-include="[name='status']"
                            name="sort"
                            class="block w-full sm:w-48 px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-prycegas-orange focus:border-prycegas-orange text-sm">
                        <option value="-order_date" {% if current_sort == '-order_date' %}selected{% endif %}>Newest First</option>
                        <option value="order_date" {% if current_sort == 'order_date' %}selected{% endif %}>Oldest First</option>
                        <option value="-total_amount" {% if current_sort == '-total_amount' %}selected{% endif %}>Highest Amount</option>
                        <option value="total_amount" {% if current_sort == 'total_amount' %}selected{% endif %}>Lowest Amount</option>
                        <option value="status" {% if current_sort == 'status' %}selected{% endif %}>Status</option>
                    </select>
                </div>
                
                <button onclick="refreshOrders()" 
                        class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-prycegas-orange">
                    <svg class="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                    </svg>
                    Refresh
                </button>
            </div>
        </div>
    </div>

    <!-- Orders List -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div id="orders-container" 
                 hx-get="{% url 'core:refresh_order_status' %}"
                 hx-trigger="every 30s"
                 hx-include="[name='status'], [name='sort']">
                {% include 'customer/order_list_partial.html' %}
            </div>
        </div>
    </div>
</div>

<script>
function refreshOrders() {
    htmx.trigger('#orders-container', 'htmx:refresh');
}

function sortOrders(sortBy) {
    const sortSelect = document.getElementById('sort-select');
    const currentSort = sortSelect.value;
    
    // Toggle sort direction if clicking the same column
    if (currentSort === sortBy) {
        sortSelect.value = '-' + sortBy;
    } else if (currentSort === '-' + sortBy) {
        sortSelect.value = sortBy;
    } else {
        sortSelect.value = '-' + sortBy;
    }
    
    // Trigger HTMX update
    htmx.trigger(sortSelect, 'change');
}

// Auto-refresh orders every 30 seconds for real-time updates
document.addEventListener('DOMContentLoaded', function() {
    // Add loading indicator for HTMX requests
    document.body.addEventListener('htmx:beforeRequest', function(evt) {
        if (evt.target.id === 'orders-container') {
            evt.target.style.opacity = '0.7';
        }
    });
    
    document.body.addEventListener('htmx:afterRequest', function(evt) {
        if (evt.target.id === 'orders-container') {
            evt.target.style.opacity = '1';
        }
    });
});
</script>
{% endblock %}