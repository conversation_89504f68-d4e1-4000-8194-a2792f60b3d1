{% extends 'base.html' %}

{% block title %}Register - Prycegas Station{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <div>
            <div class="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-prycegas-orange">
                <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                </svg>
            </div>
            <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                Create your account
            </h2>
            <p class="mt-2 text-center text-sm text-gray-600">
                Join Prycegas Station to place and track your LPG orders
            </p>
        </div>

        <!-- Display messages -->
        {% if messages %}
            <div class="space-y-2">
                {% for message in messages %}
                    <div class="rounded-md p-4 {% if message.tags == 'error' %}bg-red-50 border border-red-200{% elif message.tags == 'success' %}bg-green-50 border border-green-200{% else %}bg-blue-50 border border-blue-200{% endif %}">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                {% if message.tags == 'error' %}
                                    <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                                    </svg>
                                {% elif message.tags == 'success' %}
                                    <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                    </svg>
                                {% else %}
                                    <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                                    </svg>
                                {% endif %}
                            </div>
                            <div class="ml-3">
                                <p class="text-sm {% if message.tags == 'error' %}text-red-800{% elif message.tags == 'success' %}text-green-800{% else %}text-blue-800{% endif %}">
                                    {{ message }}
                                </p>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% endif %}

        <form class="mt-8 space-y-6 validate" method="post" 
              x-data="registrationForm()" 
              @submit="handleSubmit"
              data-validate="true">
            {% csrf_token %}
            
            <div class="space-y-4">
                <!-- Username field -->
                <div>
                    <label for="{{ form.username.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Username *
                    </label>
                    {{ form.username }}
                    <div class="mt-1 text-xs text-gray-500">
                        3-30 characters, letters, numbers, and underscores only
                    </div>
                </div>

                <!-- Email field -->
                <div>
                    <label for="{{ form.email.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Email Address *
                    </label>
                    {{ form.email }}
                    {% if form.email.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {% for error in form.email.errors %}
                                <p>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <!-- Phone number field -->
                <div>
                    <label for="{{ form.phone_number.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Phone Number *
                    </label>
                    {{ form.phone_number }}
                    <div class="mt-1 text-xs text-gray-500">
                        Format: 09123456789 or +639123456789
                    </div>
                    {% if form.phone_number.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {% for error in form.phone_number.errors %}
                                <p>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <!-- Address field -->
                <div>
                    <label for="{{ form.address.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Delivery Address *
                    </label>
                    {{ form.address }}
                    <div class="mt-1 text-xs text-gray-500">
                        Include landmarks and complete address details
                    </div>
                    {% if form.address.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {% for error in form.address.errors %}
                                <p>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <!-- Delivery instructions field -->
                <div>
                    <label for="{{ form.delivery_instructions.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Delivery Instructions
                    </label>
                    {{ form.delivery_instructions }}
                    {% if form.delivery_instructions.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {% for error in form.delivery_instructions.errors %}
                                <p>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <!-- Password fields -->
                <div>
                    <label for="{{ form.password1.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Password *
                    </label>
                    {{ form.password1 }}
                    <div class="mt-1 text-xs text-gray-500">
                        Minimum 8 characters with uppercase, lowercase, and number
                    </div>
                    {% if form.password1.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {% for error in form.password1.errors %}
                                <p>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <div>
                    <label for="{{ form.password2.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Confirm Password *
                    </label>
                    {{ form.password2 }}
                    {% if form.password2.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {% for error in form.password2.errors %}
                                <p>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
            </div>

            <div>
                <button type="submit" 
                        x-bind:disabled="isSubmitting"
                        :class="isSubmitting ? 'bg-gray-400 cursor-not-allowed' : 'bg-prycegas-orange hover:bg-prycegas-orange-dark focus:ring-prycegas-orange'"
                        class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200">
                    <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                        <svg x-show="!isSubmitting" class="h-5 w-5 text-white group-hover:text-gray-100" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"/>
                        </svg>
                        <svg x-show="isSubmitting" class="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                    </span>
                    <span x-text="isSubmitting ? 'Creating Account...' : 'Create Account'"></span>
                </button>
            </div>

            <div class="text-center">
                <p class="text-sm text-gray-600">
                    Already have an account?
                    <a href="{% url 'core:login' %}" class="font-medium text-prycegas-orange hover:text-prycegas-orange-dark">
                        Sign in here
                    </a>
                </p>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function registrationForm() {
    return {
        isSubmitting: false,
        
        init() {
            // Form validation is handled by the FormValidator class
            // This component focuses on form submission state
        },

        handleSubmit(event) {
            // Check rate limiting
            if (!formSubmissionLimiter.canMakeRequest()) {
                event.preventDefault();
                showToast('error', 'Rate Limit Exceeded', 'Please wait before submitting again.');
                return;
            }

            // Set submitting state
            this.isSubmitting = true;
            
            // The form will be validated by the FormValidator class
            // If validation fails, the form won't submit and we'll reset the state
            setTimeout(() => {
                if (this.isSubmitting) {
                    // If we're still in submitting state after a delay, 
                    // it means the form didn't submit (validation failed)
                    this.isSubmitting = false;
                }
            }, 1000);
        }
    }
}
</script>
{% endblock %}