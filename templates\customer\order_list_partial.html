<!-- Order List Partial for HTMX Updates -->
<div class="overflow-hidden">
    <!-- Desktop Table View -->
    <div class="hidden sm:block">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        <button onclick="sortOrders('order_date')" class="flex items-center space-x-1 hover:text-gray-700">
                            <span>Order Details</span>
                            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"/>
                            </svg>
                        </button>
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Delivery</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        <button onclick="sortOrders('status')" class="flex items-center space-x-1 hover:text-gray-700">
                            <span>Status</span>
                            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"/>
                            </svg>
                        </button>
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        <button onclick="sortOrders('total_amount')" class="flex items-center space-x-1 hover:text-gray-700">
                            <span>Total</span>
                            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"/>
                            </svg>
                        </button>
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
            {% for order in orders %}
            <tr class="hover:bg-gray-50 transition-colors duration-150">
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex flex-col">
                        <div class="text-sm font-medium text-gray-900">#{{ order.id }}</div>
                        <div class="text-sm text-gray-500">{{ order.order_date|date:"M d, Y g:i A" }}</div>
                        {% if order.delivery_date %}
                            <div class="text-xs text-green-600">Delivered: {{ order.delivery_date|date:"M d, Y g:i A" }}</div>
                        {% endif %}
                    </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex flex-col">
                        <div class="text-sm font-medium text-gray-900">{{ order.product.name }}</div>
                        <div class="text-sm text-gray-500">{{ order.product.size }} × {{ order.quantity }}</div>
                        <div class="text-xs text-gray-400">₱{{ order.product.price|floatformat:2 }} each</div>
                    </div>
                </td>
                <td class="px-6 py-4">
                    <div class="flex flex-col">
                        <div class="text-sm font-medium text-gray-900">
                            {% if order.delivery_type == 'pickup' %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    Pickup
                                </span>
                            {% else %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    Delivery
                                </span>
                            {% endif %}
                        </div>
                        {% if order.delivery_type == 'delivery' %}
                            <div class="text-xs text-gray-500 mt-1 max-w-xs truncate">{{ order.delivery_address }}</div>
                        {% endif %}
                    </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    {% if order.status == 'pending' %}
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 animate-pulse">
                            <svg class="-ml-0.5 mr-1.5 h-2 w-2 text-yellow-400" fill="currentColor" viewBox="0 0 8 8">
                                <circle cx="4" cy="4" r="3"/>
                            </svg>
                            Pending
                        </span>
                    {% elif order.status == 'out_for_delivery' %}
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 animate-pulse">
                            <svg class="-ml-0.5 mr-1.5 h-2 w-2 text-blue-400" fill="currentColor" viewBox="0 0 8 8">
                                <circle cx="4" cy="4" r="3"/>
                            </svg>
                            {% if order.delivery_type == 'pickup' %}Ready for Pickup{% else %}Out for Delivery{% endif %}
                        </span>
                    {% elif order.status == 'delivered' %}
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <svg class="-ml-0.5 mr-1.5 h-2 w-2 text-green-400" fill="currentColor" viewBox="0 0 8 8">
                                <circle cx="4" cy="4" r="3"/>
                            </svg>
                            Delivered
                        </span>
                    {% elif order.status == 'cancelled' %}
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                            <svg class="-ml-0.5 mr-1.5 h-2 w-2 text-red-400" fill="currentColor" viewBox="0 0 8 8">
                                <circle cx="4" cy="4" r="3"/>
                            </svg>
                            Cancelled
                        </span>
                    {% endif %}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm font-medium text-gray-900">₱{{ order.total_amount|floatformat:2 }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <a href="{% url 'core:order_detail' order.id %}" 
                       class="text-prycegas-orange hover:text-prycegas-orange-dark transition-colors duration-150">
                        View Details
                    </a>
                </td>
            </tr>
            {% if order.notes %}
            <tr class="bg-gray-50">
                <td colspan="6" class="px-6 py-2">
                    <div class="text-xs text-gray-600">
                        <strong>Notes:</strong> {{ order.notes }}
                    </div>
                </td>
            </tr>
            {% endif %}
            {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- Mobile Card View -->
    <div class="sm:hidden space-y-4">
        {% for order in orders %}
        <div class="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
            <div class="flex items-center justify-between mb-3">
                <div class="flex items-center space-x-2">
                    <span class="text-sm font-medium text-gray-900">#{{ order.id }}</span>
                    {% if order.status == 'pending' %}
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 animate-pulse">
                            Pending
                        </span>
                    {% elif order.status == 'out_for_delivery' %}
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 animate-pulse">
                            {% if order.delivery_type == 'pickup' %}Ready{% else %}Delivering{% endif %}
                        </span>
                    {% elif order.status == 'delivered' %}
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            Delivered
                        </span>
                    {% elif order.status == 'cancelled' %}
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                            Cancelled
                        </span>
                    {% endif %}
                </div>
                <a href="{% url 'core:order_detail' order.id %}" 
                   class="text-prycegas-orange hover:text-prycegas-orange-dark text-sm font-medium">
                    View Details →
                </a>
            </div>
            
            <div class="space-y-2">
                <div class="flex justify-between">
                    <span class="text-sm text-gray-500">Product:</span>
                    <span class="text-sm text-gray-900 text-right">{{ order.quantity }}x {{ order.product.name }} ({{ order.product.size }})</span>
                </div>
                
                <div class="flex justify-between">
                    <span class="text-sm text-gray-500">Total:</span>
                    <span class="text-sm font-medium text-gray-900">₱{{ order.total_amount|floatformat:2 }}</span>
                </div>
                
                <div class="flex justify-between">
                    <span class="text-sm text-gray-500">Date:</span>
                    <span class="text-sm text-gray-900">{{ order.order_date|date:"M d, Y g:i A" }}</span>
                </div>
                
                <div class="flex justify-between">
                    <span class="text-sm text-gray-500">Type:</span>
                    <span class="text-sm text-gray-900">
                        {% if order.delivery_type == 'pickup' %}
                            <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                Pickup
                            </span>
                        {% else %}
                            <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                Delivery
                            </span>
                        {% endif %}
                    </span>
                </div>
                
                {% if order.delivery_type == 'delivery' %}
                <div class="pt-2 border-t border-gray-100">
                    <span class="text-xs text-gray-500">Address:</span>
                    <p class="text-xs text-gray-700 mt-1 break-words">{{ order.delivery_address|truncatechars:60 }}</p>
                </div>
                {% endif %}
                
                {% if order.notes %}
                <div class="pt-2 border-t border-gray-100">
                    <span class="text-xs text-gray-500">Notes:</span>
                    <p class="text-xs text-gray-700 mt-1">{{ order.notes|truncatechars:80 }}</p>
                </div>
                {% endif %}
            </div>
        </div>
        {% endfor %}
    </div>
</div>

{% if not orders %}
<div class="text-center py-12">
    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
    </svg>
    <h3 class="mt-2 text-sm font-medium text-gray-900">
        {% if current_status %}
            No {{ current_status }} orders found
        {% else %}
            No orders yet
        {% endif %}
    </h3>
    <p class="mt-1 text-sm text-gray-500">
        {% if current_status %}
            Try changing the filter or place a new order.
        {% else %}
            Get started by placing your first order.
        {% endif %}
    </p>
    <div class="mt-6">
        <a href="{% url 'core:place_order' %}" 
           class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-prycegas-orange hover:bg-prycegas-orange-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-prycegas-orange">
            <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
            </svg>
            Place New Order
        </a>
    </div>
</div>
{% endif %}