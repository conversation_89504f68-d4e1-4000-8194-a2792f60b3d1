<!DOCTYPE html>
<html lang="en" class="h-full">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Prycegas Station{% endblock %}</title>
    
    <!-- TailwindCSS CDN with custom configuration -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'prycegas-black': '#1a1a1a',
                        'prycegas-orange': '#ff6b35',
                        'prycegas-orange-light': '#ff8c5a',
                        'prycegas-orange-dark': '#e55a2b',
                        'prycegas-gray': '#2d2d2d',
                        'prycegas-gray-light': '#404040',
                    }
                }
            }
        }
    </script>
    
    <!-- HTMX -->
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
    
    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    
    <!-- Unpoly -->
    <script src="https://unpkg.com/unpoly@3/unpoly.min.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/unpoly@3/unpoly.min.css">
    
    {% load static %}
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{% static 'css/custom.css' %}">
    
    <!-- Custom styles -->
    <style>
        /* Custom scrollbar for sidebar */
        .custom-scrollbar::-webkit-scrollbar {
            width: 4px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
            background: #2d2d2d;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #ff6b35;
            border-radius: 2px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #ff8c5a;
        }
        
        /* Performance optimizations for slow connections */
        .slow-connection * {
            animation-duration: 0.01ms !important;
            animation-delay: 0.01ms !important;
            transition-duration: 0.01ms !important;
            transition-delay: 0.01ms !important;
        }
        
        /* Lazy loading images */
        img.lazy {
            opacity: 0;
            transition: opacity 0.3s;
        }
        
        img.lazy.loaded {
            opacity: 1;
        }
        
        /* Touch optimization for mobile */
        .touch-optimized {
            min-height: 44px;
            min-width: 44px;
        }
        
        /* Offline indicator */
        .offline::before {
            content: "You are offline";
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: #ef4444;
            color: white;
            text-align: center;
            padding: 8px;
            z-index: 9999;
            font-size: 14px;
        }
        
        /* Pull to refresh indicator */
        #pull-refresh-indicator {
            position: fixed;
            top: -60px;
            left: 50%;
            transform: translateX(-50%);
            background: white;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            opacity: 0;
            transition: all 0.3s ease;
            z-index: 1000;
        }
        
        /* Loading states */
        .loading-skeleton {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }
        
        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }
        
        /* Scroll indicator */
        .scroll-indicator {
            position: fixed;
            top: 0;
            left: 0;
            height: 3px;
            background: #ff6b35;
            z-index: 9999;
            transition: width 0.1s ease;
        }
    </style>
    
    {% block extra_head %}{% endblock %}
</head>
<body class="h-full bg-gray-100" x-data="{ sidebarOpen: false }">
    <!-- Mobile sidebar overlay -->
    <div x-show="sidebarOpen" 
         x-transition:enter="transition-opacity ease-linear duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="transition-opacity ease-linear duration-300"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         class="fixed inset-0 z-40 lg:hidden"
         @click="sidebarOpen = false">
        <div class="fixed inset-0 bg-gray-600 bg-opacity-75"></div>
    </div>

    <!-- Sidebar -->
    <div x-show="sidebarOpen || window.innerWidth >= 1024"
         x-transition:enter="transition ease-in-out duration-300 transform"
         x-transition:enter-start="-translate-x-full"
         x-transition:enter-end="translate-x-0"
         x-transition:leave="transition ease-in-out duration-300 transform"
         x-transition:leave-start="translate-x-0"
         x-transition:leave-end="-translate-x-full"
         class="fixed inset-y-0 left-0 z-50 w-64 bg-prycegas-black lg:static lg:inset-0 lg:z-auto lg:flex lg:flex-col">
        
        {% include 'components/sidebar.html' %}
    </div>

    <!-- Main content -->
    <div class="flex flex-col flex-1 lg:ml-0">
        <!-- Top navigation bar -->
        <div class="sticky top-0 z-10 bg-white shadow-sm border-b border-gray-200 lg:hidden">
            <div class="flex items-center justify-between px-4 py-3">
                <button @click="sidebarOpen = !sidebarOpen"
                        class="text-gray-500 hover:text-gray-600 focus:outline-none focus:text-gray-600">
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
                    </svg>
                </button>
                <h1 class="text-lg font-semibold text-gray-900">Prycegas Station</h1>
                <div class="w-6"></div> <!-- Spacer for centering -->
            </div>
        </div>

        <!-- Pull to refresh indicator -->
    <div id="pull-refresh-indicator">
        <svg class="h-5 w-5 text-prycegas-orange animate-spin" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
        </svg>
    </div>

    <!-- Scroll progress indicator -->
    <div class="scroll-indicator" style="width: 0%"></div>

    <!-- Page content -->
        <main class="flex-1 relative overflow-y-auto focus:outline-none" up-main>
            <div class="py-6">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    {% block content %}{% endblock %}
                </div>
            </div>
        </main>
    </div>

    <!-- Toast notifications container -->
    <div id="toast-container" 
         class="fixed top-4 right-4 z-50 space-y-2"
         x-data="{ toasts: [] }"
         @toast.window="toasts.push($event.detail); setTimeout(() => toasts.shift(), 5000)">
        <template x-for="toast in toasts" :key="toast.id">
            <div x-show="true"
                 x-transition:enter="transform ease-out duration-300 transition"
                 x-transition:enter-start="translate-y-2 opacity-0 sm:translate-y-0 sm:translate-x-2"
                 x-transition:enter-end="translate-y-0 opacity-100 sm:translate-x-0"
                 x-transition:leave="transition ease-in duration-100"
                 x-transition:leave-start="opacity-100"
                 x-transition:leave-end="opacity-0"
                 class="max-w-sm w-full bg-white shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden">
                <div class="p-4">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <svg x-show="toast.type === 'success'" class="h-6 w-6 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                            <svg x-show="toast.type === 'error'" class="h-6 w-6 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                            <svg x-show="toast.type === 'info'" class="h-6 w-6 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                        <div class="ml-3 w-0 flex-1 pt-0.5">
                            <p class="text-sm font-medium text-gray-900" x-text="toast.title"></p>
                            <p class="mt-1 text-sm text-gray-500" x-text="toast.message"></p>
                        </div>
                        <div class="ml-4 flex-shrink-0 flex">
                            <button @click="toasts.splice(toasts.indexOf(toast), 1)"
                                    class="bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-prycegas-orange">
                                <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </template>
    </div>

    <!-- CSRF Token for HTMX -->
    {% csrf_token %}
    <meta name="csrf-token" content="{{ csrf_token }}">
    
    <!-- Utility JavaScript -->
    <script src="{% static 'js/utils.js' %}"></script>
    
    <!-- HTMX CSRF Configuration -->
    <script>
        // Configure HTMX to include CSRF token in requests
        document.addEventListener('htmx:configRequest', function(evt) {
            evt.detail.headers['X-CSRFToken'] = document.querySelector('[name=csrfmiddlewaretoken]').value;
        });
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>