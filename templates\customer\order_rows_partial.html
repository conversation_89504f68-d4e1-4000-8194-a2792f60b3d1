{% for order in orders %}
<div class="bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200 mb-4">
    <div class="p-6">
        <div class="flex items-center justify-between mb-4">
            <div class="flex items-center space-x-3">
                <h3 class="text-lg font-semibold text-gray-900">Order #{{ order.id }}</h3>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                    {% if order.status == 'pending' %}bg-yellow-100 text-yellow-800
                    {% elif order.status == 'out_for_delivery' %}bg-blue-100 text-blue-800
                    {% elif order.status == 'delivered' %}bg-green-100 text-green-800
                    {% elif order.status == 'cancelled' %}bg-red-100 text-red-800
                    {% endif %}">
                    {{ order.get_status_display }}
                </span>
            </div>
            <div class="text-right">
                <p class="text-lg font-bold text-gray-900">₱{{ order.total_amount|floatformat:2 }}</p>
                <p class="text-sm text-gray-500">{{ order.order_date|date:"M d, Y" }}</p>
            </div>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
                <h4 class="text-sm font-medium text-gray-700 mb-2">Product Details</h4>
                <p class="text-sm text-gray-900">{{ order.product.name }} - {{ order.product.size }}</p>
                <p class="text-sm text-gray-500">Quantity: {{ order.quantity }}</p>
            </div>
            <div>
                <h4 class="text-sm font-medium text-gray-700 mb-2">Delivery Information</h4>
                <p class="text-sm text-gray-900">
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                        {% if order.delivery_type == 'pickup' %}bg-gray-100 text-gray-800
                        {% else %}bg-prycegas-orange bg-opacity-10 text-prycegas-orange
                        {% endif %}">
                        {{ order.get_delivery_type_display }}
                    </span>
                </p>
                {% if order.delivery_type == 'delivery' %}
                    <p class="text-sm text-gray-500 mt-1">{{ order.delivery_address|truncatechars:50 }}</p>
                {% endif %}
            </div>
        </div>
        
        <!-- Order Progress Bar -->
        <div class="mb-4">
            <div class="flex items-center justify-between text-xs text-gray-500 mb-2">
                <span>Order Placed</span>
                <span>Out for Delivery</span>
                <span>Delivered</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
                <div class="bg-prycegas-orange h-2 rounded-full transition-all duration-300" 
                     style="width: {% if order.status == 'pending' %}25%{% elif order.status == 'out_for_delivery' %}75%{% elif order.status == 'delivered' %}100%{% else %}0%{% endif %}"></div>
            </div>
        </div>
        
        <div class="flex items-center justify-between">
            <div class="text-sm text-gray-500">
                {% if order.delivery_date %}
                    Delivered on {{ order.delivery_date|date:"M d, Y" }}
                {% elif order.status == 'out_for_delivery' %}
                    Out for delivery
                {% elif order.status == 'pending' %}
                    Processing your order
                {% endif %}
            </div>
            <a href="{% url 'core:order_detail' order.id %}" 
               class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-prycegas-orange bg-prycegas-orange bg-opacity-10 hover:bg-opacity-20 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-prycegas-orange transition-colors duration-150">
                View Details
                <svg class="ml-1 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                </svg>
            </a>
        </div>
    </div>
</div>
{% empty %}
<div class="text-center py-12">
    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
    </svg>
    <h3 class="mt-2 text-sm font-medium text-gray-900">No orders found</h3>
    <p class="mt-1 text-sm text-gray-500">You haven't placed any orders yet.</p>
    <div class="mt-6">
        <a href="{% url 'core:place_order' %}" 
           class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-prycegas-orange hover:bg-prycegas-orange-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-prycegas-orange">
            <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
            </svg>
            Place Your First Order
        </a>
    </div>
</div>
{% endfor %}

{% if has_next %}
<div id="load-more-trigger" 
     data-lazy-load="{% url 'core:lazy_load_customer_orders' %}?page={{ next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}"
     class="load-more-row text-center py-6">
    <div class="flex items-center justify-center">
        <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-prycegas-orange"></div>
        <span class="ml-3 text-gray-600">Loading more orders...</span>
    </div>
</div>
{% endif %}