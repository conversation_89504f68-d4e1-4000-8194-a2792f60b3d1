from django import forms
from django.contrib.auth.forms import UserCreationForm, AuthenticationForm
from django.contrib.auth.models import User
from django.core.validators import RegexValidator, MinLengthValidator
from django.utils.html import strip_tags
from django.core.exceptions import ValidationError
import re
from .models import CustomerProfile, Order, LPGProduct, DeliveryLog


class CustomerRegistrationForm(UserCreationForm):
    """
    Customer registration form with additional profile fields and enhanced validation
    Requirements: 1.1, 1.2, 10.1, 10.2, 10.3 - Customer registration with validation and security
    """
    email = forms.EmailField(
        required=True,
        validators=[
            RegexValidator(
                regex=r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
                message='Please enter a valid email address.'
            )
        ],
        widget=forms.EmailInput(attrs={
            'class': 'mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-prycegas-orange focus:border-prycegas-orange',
            'placeholder': 'Enter your email address',
            'x-model': 'email',
            '@input.debounce.500ms': 'validateEmail()',
            'autocomplete': 'email'
        })
    )
    phone_number = forms.CharField(
        max_length=15,
        required=True,
        validators=[
            RegexValidator(
                regex=r'^(\+63|0)[0-9]{10}$',
                message='Please enter a valid Philippine phone number (e.g., +639123456789 or 09123456789).'
            )
        ],
        widget=forms.TextInput(attrs={
            'class': 'mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-prycegas-orange focus:border-prycegas-orange',
            'placeholder': 'Enter your phone number (e.g., 09123456789)',
            'x-model': 'phoneNumber',
            '@input': 'validatePhoneNumber()',
            'autocomplete': 'tel'
        })
    )
    address = forms.CharField(
        min_length=10,
        max_length=500,
        validators=[
            MinLengthValidator(10, 'Address must be at least 10 characters long.')
        ],
        widget=forms.Textarea(attrs={
            'class': 'mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-prycegas-orange focus:border-prycegas-orange',
            'placeholder': 'Enter your complete delivery address with landmarks',
            'rows': 3,
            'x-model': 'address',
            '@input': 'validateAddress()',
            'autocomplete': 'street-address'
        })
    )
    delivery_instructions = forms.CharField(
        required=False,
        max_length=200,
        widget=forms.Textarea(attrs={
            'class': 'mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-prycegas-orange focus:border-prycegas-orange',
            'placeholder': 'Any special delivery instructions (optional)',
            'rows': 2,
            'maxlength': '200'
        })
    )

    class Meta:
        model = User
        fields = ('username', 'email', 'password1', 'password2')

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Add CSS classes and validation attributes to default fields
        self.fields['username'].widget.attrs.update({
            'class': 'mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-prycegas-orange focus:border-prycegas-orange',
            'placeholder': 'Choose a username (3-30 characters)',
            'x-model': 'username',
            '@input.debounce.500ms': 'validateUsername()',
            'autocomplete': 'username',
            'minlength': '3',
            'maxlength': '30'
        })
        self.fields['password1'].widget.attrs.update({
            'class': 'mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-prycegas-orange focus:border-prycegas-orange',
            'placeholder': 'Enter your password (min. 8 characters)',
            'x-model': 'password1',
            '@input': 'validatePassword()',
            'autocomplete': 'new-password',
            'minlength': '8'
        })
        self.fields['password2'].widget.attrs.update({
            'class': 'mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-prycegas-orange focus:border-prycegas-orange',
            'placeholder': 'Confirm your password',
            'x-model': 'password2',
            '@input': 'validatePasswordMatch()',
            'autocomplete': 'new-password'
        })
        
        # Add enhanced validation to username field
        self.fields['username'].validators.append(
            RegexValidator(
                regex=r'^[a-zA-Z0-9_]+$',
                message='Username can only contain letters, numbers, and underscores.'
            )
        )
        self.fields['username'].validators.append(
            MinLengthValidator(3, 'Username must be at least 3 characters long.')
        )

    def clean_username(self):
        """Enhanced username validation with sanitization"""
        username = self.cleaned_data.get('username')
        if username:
            # Sanitize input
            username = strip_tags(username).strip()
            
            # Check length
            if len(username) < 3:
                raise ValidationError('Username must be at least 3 characters long.')
            if len(username) > 30:
                raise ValidationError('Username cannot exceed 30 characters.')
            
            # Check for valid characters
            if not re.match(r'^[a-zA-Z0-9_]+$', username):
                raise ValidationError('Username can only contain letters, numbers, and underscores.')
            
            # Check uniqueness
            if User.objects.filter(username__iexact=username).exists():
                raise ValidationError('This username is already taken.')
        
        return username

    def clean_email(self):
        """Enhanced email validation with sanitization"""
        email = self.cleaned_data.get('email')
        if email:
            # Sanitize input
            email = strip_tags(email).strip().lower()
            
            # Additional email validation
            if not re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', email):
                raise ValidationError('Please enter a valid email address.')
            
            # Check uniqueness
            if User.objects.filter(email__iexact=email).exists():
                raise ValidationError('A user with this email already exists.')
        
        return email

    def clean_phone_number(self):
        """Enhanced phone number validation with sanitization"""
        phone = self.cleaned_data.get('phone_number')
        if phone:
            # Sanitize input - remove all non-digit characters except +
            phone = re.sub(r'[^\d+]', '', phone)
            
            # Normalize Philippine phone numbers
            if phone.startswith('0'):
                phone = '+63' + phone[1:]
            elif phone.startswith('63'):
                phone = '+' + phone
            elif not phone.startswith('+63'):
                raise ValidationError('Please enter a valid Philippine phone number.')
            
            # Validate format
            if not re.match(r'^\+63[0-9]{10}$', phone):
                raise ValidationError('Please enter a valid Philippine phone number (e.g., +639123456789).')
        
        return phone

    def clean_address(self):
        """Enhanced address validation with sanitization"""
        address = self.cleaned_data.get('address')
        if address:
            # Sanitize input
            address = strip_tags(address).strip()
            
            # Check minimum length
            if len(address) < 10:
                raise ValidationError('Address must be at least 10 characters long.')
            
            # Check maximum length
            if len(address) > 500:
                raise ValidationError('Address cannot exceed 500 characters.')
        
        return address

    def clean_delivery_instructions(self):
        """Sanitize delivery instructions"""
        instructions = self.cleaned_data.get('delivery_instructions')
        if instructions:
            # Sanitize input
            instructions = strip_tags(instructions).strip()
            
            # Check maximum length
            if len(instructions) > 200:
                raise ValidationError('Delivery instructions cannot exceed 200 characters.')
        
        return instructions

    def save(self, commit=True):
        """Save user and create customer profile"""
        user = super().save(commit=False)
        user.email = self.cleaned_data['email']
        if commit:
            user.save()
            # Create customer profile
            CustomerProfile.objects.create(
                user=user,
                phone_number=self.cleaned_data['phone_number'],
                address=self.cleaned_data['address'],
                delivery_instructions=self.cleaned_data['delivery_instructions']
            )
        return user


class CustomerLoginForm(AuthenticationForm):
    """
    Custom login form with styling
    Requirements: 1.4 - Customer login functionality
    """
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['username'].widget.attrs.update({
            'class': 'mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-prycegas-orange focus:border-prycegas-orange',
            'placeholder': 'Enter your username'
        })
        self.fields['password'].widget.attrs.update({
            'class': 'mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-prycegas-orange focus:border-prycegas-orange',
            'placeholder': 'Enter your password'
        })


class CustomerProfileForm(forms.ModelForm):
    """
    Form for updating customer profile information
    Requirements: 1.2 - Customer profile management
    """
    class Meta:
        model = CustomerProfile
        fields = ['phone_number', 'address', 'delivery_instructions']
        widgets = {
            'phone_number': forms.TextInput(attrs={
                'class': 'mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-prycegas-orange focus:border-prycegas-orange',
                'placeholder': 'Enter your phone number'
            }),
            'address': forms.Textarea(attrs={
                'class': 'mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-prycegas-orange focus:border-prycegas-orange',
                'placeholder': 'Enter your delivery address',
                'rows': 3
            }),
            'delivery_instructions': forms.Textarea(attrs={
                'class': 'mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-prycegas-orange focus:border-prycegas-orange',
                'placeholder': 'Any special delivery instructions (optional)',
                'rows': 2
            })
        }


class UserUpdateForm(forms.ModelForm):
    """
    Form for updating user information
    Requirements: 1.2 - Customer profile management
    """
    class Meta:
        model = User
        fields = ['first_name', 'last_name', 'email']
        widgets = {
            'first_name': forms.TextInput(attrs={
                'class': 'mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-prycegas-orange focus:border-prycegas-orange',
                'placeholder': 'Enter your first name'
            }),
            'last_name': forms.TextInput(attrs={
                'class': 'mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-prycegas-orange focus:border-prycegas-orange',
                'placeholder': 'Enter your last name'
            }),
            'email': forms.EmailInput(attrs={
                'class': 'mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-prycegas-orange focus:border-prycegas-orange',
                'placeholder': 'Enter your email address'
            })
        }

    def clean_email(self):
        """Validate that email is unique (excluding current user)"""
        email = self.cleaned_data.get('email')
        if User.objects.filter(email=email).exclude(pk=self.instance.pk).exists():
            raise forms.ValidationError("A user with this email already exists.")
        return email


class OrderForm(forms.ModelForm):
    """
    Form for placing LPG orders with product selection and delivery options
    Requirements: 2.1, 2.2, 2.3, 2.4, 2.5 - Order placement with validation
    """
    product = forms.ModelChoiceField(
        queryset=LPGProduct.objects.filter(is_active=True, current_stock__gt=0),
        empty_label="Select a product",
        widget=forms.Select(attrs={
            'class': 'mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-prycegas-orange focus:border-prycegas-orange',
            'hx-get': '/check-stock/',
            'hx-target': '#stock-info',
            'hx-trigger': 'change',
            'hx-include': '[name="quantity"]'
        })
    )
    
    quantity = forms.IntegerField(
        min_value=1,
        initial=1,
        widget=forms.NumberInput(attrs={
            'class': 'mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-prycegas-orange focus:border-prycegas-orange',
            'placeholder': 'Enter quantity',
            'hx-get': '/check-stock/',
            'hx-target': '#stock-info',
            'hx-trigger': 'keyup changed delay:500ms',
            'hx-include': '[name="product"]'
        })
    )
    
    delivery_type = forms.ChoiceField(
        choices=Order.DELIVERY_CHOICES,
        initial='delivery',
        widget=forms.RadioSelect(attrs={
            'class': 'focus:ring-prycegas-orange h-4 w-4 text-prycegas-orange border-gray-300',
            'x-model': 'deliveryType'
        })
    )
    
    delivery_address = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-prycegas-orange focus:border-prycegas-orange',
            'placeholder': 'Enter delivery address (required for delivery orders)',
            'rows': 3,
            'x-show': 'deliveryType === "delivery"',
            'x-transition': True
        })
    )
    
    notes = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-prycegas-orange focus:border-prycegas-orange',
            'placeholder': 'Any special instructions or notes (optional)',
            'rows': 2
        })
    )

    class Meta:
        model = Order
        fields = ['product', 'quantity', 'delivery_type', 'delivery_address', 'notes']

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        # Set delivery address from user profile if available
        if self.user and hasattr(self.user, 'customer_profile'):
            self.fields['delivery_address'].initial = self.user.customer_profile.address

    def clean_quantity(self):
        """Enhanced quantity validation"""
        quantity = self.cleaned_data.get('quantity')
        if quantity is not None:
            if quantity < 1:
                raise ValidationError('Quantity must be at least 1.')
            if quantity > 100:  # Reasonable maximum
                raise ValidationError('Quantity cannot exceed 100 units per order.')
        return quantity

    def clean_delivery_address(self):
        """Enhanced delivery address validation with sanitization"""
        address = self.cleaned_data.get('delivery_address')
        if address:
            # Sanitize input
            address = strip_tags(address).strip()
            
            # Check minimum length for delivery addresses
            if len(address) < 10:
                raise ValidationError('Delivery address must be at least 10 characters long.')
            
            # Check maximum length
            if len(address) > 500:
                raise ValidationError('Delivery address cannot exceed 500 characters.')
        
        return address

    def clean_notes(self):
        """Sanitize order notes"""
        notes = self.cleaned_data.get('notes')
        if notes:
            # Sanitize input
            notes = strip_tags(notes).strip()
            
            # Check maximum length
            if len(notes) > 500:
                raise ValidationError('Notes cannot exceed 500 characters.')
        
        return notes

    def clean(self):
        cleaned_data = super().clean()
        product = cleaned_data.get('product')
        quantity = cleaned_data.get('quantity')
        delivery_type = cleaned_data.get('delivery_type')
        delivery_address = cleaned_data.get('delivery_address')

        # Validate stock availability with enhanced error messages
        if product and quantity:
            if not product.is_active:
                raise ValidationError({
                    'product': 'This product is currently unavailable.'
                })
            
            if product.current_stock <= 0:
                raise ValidationError({
                    'product': 'This product is currently out of stock.'
                })
            
            if not product.can_fulfill_order(quantity):
                raise ValidationError({
                    'quantity': f'Insufficient stock. Only {product.current_stock} units available.'
                })

        # Validate delivery address for delivery orders
        if delivery_type == 'delivery':
            if not delivery_address or len(delivery_address.strip()) < 10:
                raise ValidationError({
                    'delivery_address': 'A complete delivery address is required for delivery orders.'
                })

        return cleaned_data

    def save(self, commit=True):
        order = super().save(commit=False)
        if self.user:
            order.customer = self.user
        
        # Calculate total amount
        if order.product and order.quantity:
            order.total_amount = order.product.price * order.quantity
        
        # Set delivery address from form or user profile
        if order.delivery_type == 'pickup':
            order.delivery_address = "Pickup at station"
        elif not order.delivery_address and self.user and hasattr(self.user, 'customer_profile'):
            order.delivery_address = self.user.customer_profile.address

        if commit:
            order.save()
            # Reduce product stock
            product = order.product
            product.current_stock -= order.quantity
            product.save()

        return order


class DeliveryLogForm(forms.ModelForm):
    """
    Form for logging distributor deliveries with automatic inventory adjustment
    Requirements: 6.2, 6.4 - Delivery logging with form validation
    """
    product = forms.ModelChoiceField(
        queryset=LPGProduct.objects.filter(is_active=True),
        empty_label="Select a product",
        widget=forms.Select(attrs={
            'class': 'mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-prycegas-orange focus:border-prycegas-orange',
            'x-model': 'selectedProduct',
            '@change': 'calculateTotal()'
        })
    )
    
    quantity_received = forms.IntegerField(
        min_value=1,
        widget=forms.NumberInput(attrs={
            'class': 'mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-prycegas-orange focus:border-prycegas-orange',
            'placeholder': 'Enter quantity received',
            'x-model': 'quantity',
            '@input': 'calculateTotal()'
        })
    )
    
    supplier = forms.CharField(
        max_length=100,
        widget=forms.TextInput(attrs={
            'class': 'mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-prycegas-orange focus:border-prycegas-orange',
            'placeholder': 'Enter supplier/distributor name'
        })
    )
    
    delivery_date = forms.DateTimeField(
        widget=forms.DateTimeInput(attrs={
            'class': 'mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-prycegas-orange focus:border-prycegas-orange',
            'type': 'datetime-local'
        })
    )
    
    cost_per_unit = forms.DecimalField(
        max_digits=10,
        decimal_places=2,
        min_value=0.01,
        widget=forms.NumberInput(attrs={
            'class': 'mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-prycegas-orange focus:border-prycegas-orange',
            'placeholder': 'Enter cost per unit',
            'step': '0.01',
            'x-model': 'costPerUnit',
            '@input': 'calculateTotal()'
        })
    )
    
    total_cost = forms.DecimalField(
        max_digits=10,
        decimal_places=2,
        min_value=0.01,
        required=False,
        widget=forms.NumberInput(attrs={
            'class': 'mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-50 placeholder-gray-400 focus:outline-none focus:ring-prycegas-orange focus:border-prycegas-orange',
            'placeholder': 'Auto-calculated',
            'step': '0.01',
            'readonly': True,
            'x-model': 'totalCost'
        })
    )
    
    notes = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-prycegas-orange focus:border-prycegas-orange',
            'placeholder': 'Any additional notes about the delivery (optional)',
            'rows': 3
        })
    )

    class Meta:
        model = DeliveryLog
        fields = ['product', 'quantity_received', 'supplier', 'delivery_date', 'cost_per_unit', 'total_cost', 'notes']

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Set default delivery date to current time
        if not self.initial.get('delivery_date'):
            from django.utils import timezone
            self.fields['delivery_date'].initial = timezone.now().strftime('%Y-%m-%dT%H:%M')

    def clean_quantity_received(self):
        """Enhanced quantity validation"""
        quantity = self.cleaned_data.get('quantity_received')
        if quantity is not None:
            if quantity < 1:
                raise ValidationError('Quantity received must be at least 1.')
            if quantity > 10000:  # Reasonable maximum for deliveries
                raise ValidationError('Quantity received cannot exceed 10,000 units.')
        return quantity

    def clean_supplier(self):
        """Sanitize supplier name"""
        supplier = self.cleaned_data.get('supplier')
        if supplier:
            # Sanitize input
            supplier = strip_tags(supplier).strip()
            
            # Check minimum length
            if len(supplier) < 2:
                raise ValidationError('Supplier name must be at least 2 characters long.')
            
            # Check maximum length
            if len(supplier) > 100:
                raise ValidationError('Supplier name cannot exceed 100 characters.')
        
        return supplier

    def clean_cost_per_unit(self):
        """Enhanced cost validation"""
        cost = self.cleaned_data.get('cost_per_unit')
        if cost is not None:
            if cost <= 0:
                raise ValidationError('Cost per unit must be greater than zero.')
            if cost > 10000:  # Reasonable maximum
                raise ValidationError('Cost per unit cannot exceed ₱10,000.')
        return cost

    def clean_notes(self):
        """Sanitize delivery notes"""
        notes = self.cleaned_data.get('notes')
        if notes:
            # Sanitize input
            notes = strip_tags(notes).strip()
            
            # Check maximum length
            if len(notes) > 500:
                raise ValidationError('Notes cannot exceed 500 characters.')
        
        return notes

    def clean(self):
        cleaned_data = super().clean()
        quantity_received = cleaned_data.get('quantity_received')
        cost_per_unit = cleaned_data.get('cost_per_unit')
        total_cost = cleaned_data.get('total_cost')

        # Calculate total cost if not provided
        if quantity_received and cost_per_unit and not total_cost:
            cleaned_data['total_cost'] = quantity_received * cost_per_unit

        # Validate that total cost matches calculation
        if quantity_received and cost_per_unit and total_cost:
            expected_total = quantity_received * cost_per_unit
            if abs(total_cost - expected_total) > 0.01:  # Allow for small rounding differences
                raise ValidationError({
                    'total_cost': f'Total cost should be ₱{expected_total:.2f} (quantity × cost per unit).'
                })

        return cleaned_data