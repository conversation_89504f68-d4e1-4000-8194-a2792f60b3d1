from django.urls import path
from .views import (
    test_base_template, customer_register, customer_login, customer_logout,
    customer_profile, validate_username, validate_email, customer_dashboard,
    place_order, check_stock, order_history, order_detail, refresh_order_status,
    refresh_dashboard_orders, dealer_dashboard, refresh_dashboard_stats,
    refresh_recent_activity, order_management, update_order_status,
    order_detail_modal, bulk_order_operations, refresh_order_table,
    inventory_management, log_delivery, refresh_inventory_dashboard,
    refresh_stock_movements, get_delivery_form, reports_dashboard,
    sales_report, stock_report, print_report, lazy_load_orders,
    lazy_load_customer_orders
)

app_name = 'core'

urlpatterns = [
    path('', test_base_template, name='home'),
    path('test/', test_base_template, name='test_base'),

    # Authentication URLs
    path('register/', customer_register, name='register'),
    path('login/', customer_login, name='login'),
    path('logout/', customer_logout, name='logout'),
    path('profile/', customer_profile, name='profile'),

    # HTMX validation endpoints
    path('validate-username/', validate_username, name='validate_username'),
    path('validate-email/', validate_email, name='validate_email'),

    # Customer URLs
    path('customer/dashboard/', customer_dashboard, name='customer_dashboard'),
    path('customer/order/', place_order, name='place_order'),
    path('customer/history/', order_history, name='order_history'),
    path('customer/order/<int:order_id>/', order_detail, name='order_detail'),
    
    # HTMX endpoints for order system
    path('check-stock/', check_stock, name='check_stock'),
    path('refresh-orders/', refresh_order_status, name='refresh_order_status'),
    path('refresh-dashboard/', refresh_dashboard_orders, name='refresh_dashboard_orders'),

    # Dealer URLs
    path('dealer/dashboard/', dealer_dashboard, name='dealer_dashboard'),
    path('dealer/orders/', order_management, name='order_management'),
    path('dealer/inventory/', inventory_management, name='inventory_management'),
    path('dealer/reports/', reports_dashboard, name='reports_dashboard'),
    path('dealer/reports/sales/', sales_report, name='sales_report'),
    path('dealer/reports/stock/', stock_report, name='stock_report'),
    path('dealer/reports/print/', print_report, name='print_report'),
    
    # HTMX/Unpoly endpoints for dealer dashboard
    path('dealer/refresh-stats/', refresh_dashboard_stats, name='refresh_dashboard_stats'),
    path('dealer/refresh-activity/', refresh_recent_activity, name='refresh_recent_activity'),
    
    # Order management endpoints
    path('dealer/orders/update/<int:order_id>/', update_order_status, name='update_order_status'),
    path('dealer/orders/detail/<int:order_id>/', order_detail_modal, name='order_detail_modal'),
    path('dealer/orders/bulk/', bulk_order_operations, name='bulk_order_operations'),
    path('dealer/orders/refresh/', refresh_order_table, name='refresh_order_table'),
    
    # Inventory management endpoints
    path('dealer/inventory/log-delivery/', log_delivery, name='log_delivery'),
    path('dealer/inventory/refresh/', refresh_inventory_dashboard, name='refresh_inventory_dashboard'),
    path('dealer/inventory/movements/', refresh_stock_movements, name='refresh_stock_movements'),
    path('dealer/inventory/delivery-form/', get_delivery_form, name='get_delivery_form'),
    
    # Lazy loading endpoints for performance optimization
    path('api/orders/lazy/', lazy_load_orders, name='lazy_load_orders'),
    path('api/customer/orders/lazy/', lazy_load_customer_orders, name='lazy_load_customer_orders'),
]
