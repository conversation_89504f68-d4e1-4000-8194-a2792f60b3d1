{% extends 'base.html' %}
{% load static %}

{% block title %}Reports Dashboard - Prycegas Station{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
    <!-- Include Sidebar -->
    {% include 'components/sidebar.html' %}
    
    <!-- Main Content -->
    <div class="lg:ml-64">
        <!-- Header -->
        <div class="bg-white shadow-sm border-b border-gray-200">
            <div class="px-4 sm:px-6 lg:px-8 py-4">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">Reports Dashboard</h1>
                        <p class="text-sm text-gray-600 mt-1">Generate and view business reports</p>
                    </div>
                    <div class="flex items-center space-x-3">
                        <span class="text-sm text-gray-500">{{ request.user.username }}</span>
                        <div class="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center">
                            <span class="text-white text-sm font-medium">{{ request.user.username|first|upper }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="px-4 sm:px-6 lg:px-8 py-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <!-- Current Month Orders -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-orange-100 rounded-md flex items-center justify-center">
                                <svg class="w-5 h-5 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">This Month Orders</p>
                            <p class="text-2xl font-bold text-gray-900">{{ quick_stats.current_month_orders }}</p>
                        </div>
                    </div>
                </div>

                <!-- Current Month Revenue -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-green-100 rounded-md flex items-center justify-center">
                                <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">This Month Revenue</p>
                            <p class="text-2xl font-bold text-gray-900">₱{{ quick_stats.current_month_revenue|floatformat:2 }}</p>
                        </div>
                    </div>
                </div>

                <!-- Total Stock Value -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-blue-100 rounded-md flex items-center justify-center">
                                <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total Stock Value</p>
                            <p class="text-2xl font-bold text-gray-900">₱{{ quick_stats.total_stock_value|floatformat:2 }}</p>
                        </div>
                    </div>
                </div>

                <!-- Low Stock Alerts -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-red-100 rounded-md flex items-center justify-center">
                                <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Low Stock Items</p>
                            <p class="text-2xl font-bold text-gray-900">{{ quick_stats.low_stock_count }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Report Options -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Sales Report Card -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                    <div class="px-6 py-4 bg-gradient-to-r from-orange-500 to-orange-600">
                        <h3 class="text-lg font-semibold text-white">Sales Reports</h3>
                        <p class="text-orange-100 text-sm mt-1">Analyze sales performance and revenue trends</p>
                    </div>
                    <div class="p-6">
                        <form method="get" action="{% url 'core:sales_report' %}" class="space-y-4">
                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                <div>
                                    <label for="sales_date_from" class="block text-sm font-medium text-gray-700 mb-1">From Date</label>
                                    <input type="date" id="sales_date_from" name="date_from" 
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent">
                                </div>
                                <div>
                                    <label for="sales_date_to" class="block text-sm font-medium text-gray-700 mb-1">To Date</label>
                                    <input type="date" id="sales_date_to" name="date_to" 
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent">
                                </div>
                            </div>
                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                <div>
                                    <label for="sales_product" class="block text-sm font-medium text-gray-700 mb-1">Product</label>
                                    <select id="sales_product" name="product" 
                                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent">
                                        <option value="">All Products</option>
                                        {% for product in products %}
                                            <option value="{{ product.id }}">{{ product.name }} - {{ product.size }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div>
                                    <label for="sales_customer" class="block text-sm font-medium text-gray-700 mb-1">Customer</label>
                                    <select id="sales_customer" name="customer" 
                                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent">
                                        <option value="">All Customers</option>
                                        {% for customer in customers %}
                                            <option value="{{ customer.id }}">{{ customer.username }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="flex justify-end">
                                <button type="submit" 
                                        class="bg-orange-500 hover:bg-orange-600 text-white px-6 py-2 rounded-md font-medium transition-colors duration-200">
                                    Generate Sales Report
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Stock Report Card -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                    <div class="px-6 py-4 bg-gradient-to-r from-blue-500 to-blue-600">
                        <h3 class="text-lg font-semibold text-white">Stock Reports</h3>
                        <p class="text-blue-100 text-sm mt-1">Monitor inventory levels and stock movements</p>
                    </div>
                    <div class="p-6">
                        <form method="get" action="{% url 'core:stock_report' %}" class="space-y-4">
                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                <div>
                                    <label for="stock_date_from" class="block text-sm font-medium text-gray-700 mb-1">From Date</label>
                                    <input type="date" id="stock_date_from" name="date_from" 
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                </div>
                                <div>
                                    <label for="stock_date_to" class="block text-sm font-medium text-gray-700 mb-1">To Date</label>
                                    <input type="date" id="stock_date_to" name="date_to" 
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                </div>
                            </div>
                            <div>
                                <label for="stock_product" class="block text-sm font-medium text-gray-700 mb-1">Product</label>
                                <select id="stock_product" name="product" 
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <option value="">All Products</option>
                                    {% for product in products %}
                                        <option value="{{ product.id }}">{{ product.name }} - {{ product.size }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="flex justify-end">
                                <button type="submit" 
                                        class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-md font-medium transition-colors duration-200">
                                    Generate Stock Report
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Recent Activity Summary -->
            <div class="mt-8 bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">Quick Summary</h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-orange-600">{{ quick_stats.current_month_orders }}</div>
                            <div class="text-sm text-gray-600">Orders This Month</div>
                            <div class="text-xs text-gray-500 mt-1">
                                {% if quick_stats.last_month_orders > 0 %}
                                    {% widthratio quick_stats.current_month_orders quick_stats.last_month_orders 100 %}% vs last month
                                {% else %}
                                    First month data
                                {% endif %}
                            </div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-green-600">₱{{ quick_stats.current_month_revenue|floatformat:0 }}</div>
                            <div class="text-sm text-gray-600">Revenue This Month</div>
                            <div class="text-xs text-gray-500 mt-1">
                                {% if quick_stats.last_month_revenue > 0 %}
                                    {% widthratio quick_stats.current_month_revenue quick_stats.last_month_revenue 100 %}% vs last month
                                {% else %}
                                    First month data
                                {% endif %}
                            </div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-blue-600">₱{{ quick_stats.recent_deliveries_value|floatformat:0 }}</div>
                            <div class="text-sm text-gray-600">Deliveries (30 days)</div>
                            <div class="text-xs text-gray-500 mt-1">Stock replenishment value</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Set default dates to last 30 days
document.addEventListener('DOMContentLoaded', function() {
    const today = new Date();
    const thirtyDaysAgo = new Date(today.getTime() - (30 * 24 * 60 * 60 * 1000));
    
    const formatDate = (date) => {
        return date.toISOString().split('T')[0];
    };
    
    // Set default dates for sales report
    const salesFromDate = document.getElementById('sales_date_from');
    const salesToDate = document.getElementById('sales_date_to');
    if (salesFromDate && !salesFromDate.value) salesFromDate.value = formatDate(thirtyDaysAgo);
    if (salesToDate && !salesToDate.value) salesToDate.value = formatDate(today);
    
    // Set default dates for stock report
    const stockFromDate = document.getElementById('stock_date_from');
    const stockToDate = document.getElementById('stock_date_to');
    if (stockFromDate && !stockFromDate.value) stockFromDate.value = formatDate(thirtyDaysAgo);
    if (stockToDate && !stockToDate.value) stockToDate.value = formatDate(today);
});
</script>
{% endblock %}