{% extends 'base.html' %}

{% block title %}Order Management - Prycegas Station{% endblock %}

{% block content %}
<div class="order-management" x-data="orderManagement()">
    <!-- Page Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Order Management</h1>
                <p class="mt-2 text-gray-600">Manage customer orders and track delivery status</p>
            </div>
            <div class="flex items-center space-x-4">
                <!-- Auto-refresh toggle -->
                <div class="flex items-center">
                    <label class="flex items-center cursor-pointer">
                        <input type="checkbox" x-model="autoRefresh" @change="toggleAutoRefresh()" 
                               class="sr-only">
                        <div class="relative">
                            <div class="block bg-gray-600 w-14 h-8 rounded-full"></div>
                            <div class="dot absolute left-1 top-1 bg-white w-6 h-6 rounded-full transition"
                                 :class="{ 'transform translate-x-6 bg-prycegas-orange': autoRefresh }"></div>
                        </div>
                        <span class="ml-3 text-sm text-gray-700">Auto-refresh</span>
                    </label>
                </div>
                <!-- Manual refresh button -->
                <button @click="refreshTable()"
                        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-prycegas-orange hover:bg-prycegas-orange-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-prycegas-orange transition-colors duration-150">
                    <svg class="mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                    </svg>
                    Refresh
                </button>
            </div>
        </div>
    </div>

    <!-- Summary Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Orders</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ summary_stats.total_orders }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Pending</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ summary_stats.pending_count }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-prycegas-orange rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"/>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Out for Delivery</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ summary_stats.out_for_delivery_count }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Delivered</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ summary_stats.delivered_count }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="bg-white shadow rounded-lg mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Filters</h3>
        </div>
        <div class="p-6">
            <form method="GET" class="space-y-4" x-ref="filterForm">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <!-- Status Filter -->
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700">Status</label>
                        <select name="status" id="status" 
                                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-prycegas-orange focus:border-prycegas-orange"
                                @change="applyFilters()">
                            <option value="">All Statuses</option>
                            {% for value, label in status_choices %}
                                <option value="{{ value }}" {% if current_filters.status == value %}selected{% endif %}>
                                    {{ label }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- Delivery Type Filter -->
                    <div>
                        <label for="delivery_type" class="block text-sm font-medium text-gray-700">Delivery Type</label>
                        <select name="delivery_type" id="delivery_type" 
                                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-prycegas-orange focus:border-prycegas-orange"
                                @change="applyFilters()">
                            <option value="">All Types</option>
                            {% for value, label in delivery_choices %}
                                <option value="{{ value }}" {% if current_filters.delivery_type == value %}selected{% endif %}>
                                    {{ label }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- Date From -->
                    <div>
                        <label for="date_from" class="block text-sm font-medium text-gray-700">From Date</label>
                        <input type="date" name="date_from" id="date_from" 
                               value="{{ current_filters.date_from }}"
                               class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-prycegas-orange focus:border-prycegas-orange"
                               @change="applyFilters()">
                    </div>

                    <!-- Date To -->
                    <div>
                        <label for="date_to" class="block text-sm font-medium text-gray-700">To Date</label>
                        <input type="date" name="date_to" id="date_to" 
                               value="{{ current_filters.date_to }}"
                               class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-prycegas-orange focus:border-prycegas-orange"
                               @change="applyFilters()">
                    </div>
                </div>

                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
                    <!-- Search -->
                    <div class="flex-1 max-w-lg">
                        <label for="search" class="block text-sm font-medium text-gray-700">Search</label>
                        <div class="mt-1 relative">
                            <input type="text" name="search" id="search" 
                                   value="{{ current_filters.search }}"
                                   placeholder="Search by customer name or order ID..."
                                   class="block w-full px-3 py-2 pl-10 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-prycegas-orange focus:border-prycegas-orange"
                                   @input="debounceSearch($event)">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                                </svg>
                            </div>
                        </div>
                    </div>

                    <!-- Sort -->
                    <div class="sm:ml-4">
                        <label for="sort" class="block text-sm font-medium text-gray-700">Sort By</label>
                        <select name="sort" id="sort" 
                                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-prycegas-orange focus:border-prycegas-orange"
                                @change="applyFilters()">
                            <option value="-order_date" {% if current_filters.sort == '-order_date' %}selected{% endif %}>Newest First</option>
                            <option value="order_date" {% if current_filters.sort == 'order_date' %}selected{% endif %}>Oldest First</option>
                            <option value="-total_amount" {% if current_filters.sort == '-total_amount' %}selected{% endif %}>Highest Amount</option>
                            <option value="total_amount" {% if current_filters.sort == 'total_amount' %}selected{% endif %}>Lowest Amount</option>
                            <option value="customer__username" {% if current_filters.sort == 'customer__username' %}selected{% endif %}>Customer A-Z</option>
                            <option value="-customer__username" {% if current_filters.sort == '-customer__username' %}selected{% endif %}>Customer Z-A</option>
                            <option value="status" {% if current_filters.sort == 'status' %}selected{% endif %}>Status A-Z</option>
                            <option value="-status" {% if current_filters.sort == '-status' %}selected{% endif %}>Status Z-A</option>
                        </select>
                    </div>

                    <!-- Clear Filters -->
                    <div class="sm:ml-4">
                        <label class="block text-sm font-medium text-gray-700">&nbsp;</label>
                        <button type="button" @click="clearFilters()"
                                class="mt-1 inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-prycegas-orange">
                            Clear Filters
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Bulk Operations -->
    <div class="bg-white shadow rounded-lg mb-6" x-show="selectedOrders.length > 0" x-transition>
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900">
                    Bulk Operations (<span x-text="selectedOrders.length"></span> selected)
                </h3>
                <button @click="clearSelection()" 
                        class="text-sm text-gray-500 hover:text-gray-700">
                    Clear Selection
                </button>
            </div>
        </div>
        <div class="p-6">
            <div class="flex flex-wrap gap-3">
                <button @click="bulkOperation('mark_out_for_delivery')"
                        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-prycegas-orange hover:bg-prycegas-orange-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-prycegas-orange">
                    Mark as Out for Delivery
                </button>
                <button @click="bulkOperation('mark_delivered')"
                        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                    Mark as Delivered
                </button>
                <button @click="bulkOperation('cancel_orders')"
                        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                    Cancel Orders
                </button>
            </div>
        </div>
    </div>

    <!-- Orders Table -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Orders</h3>
        </div>
        <div id="order-table-container">
            {% include 'dealer/order_table_partial.html' %}
        </div>
    </div>

    <!-- Order Detail Modal -->
    <div id="order-detail-modal"></div>
</div>

<style>
/* Custom toggle switch styling */
.dot {
    transition: transform 0.3s ease, background-color 0.3s ease;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function orderManagement() {
    return {
        autoRefresh: false,
        refreshInterval: null,
        selectedOrders: [],
        searchTimeout: null,

        init() {
            // Initialize any needed setup
        },

        toggleAutoRefresh() {
            if (this.autoRefresh) {
                this.startAutoRefresh();
            } else {
                this.stopAutoRefresh();
            }
        },

        startAutoRefresh() {
            this.refreshInterval = setInterval(() => {
                this.refreshTable();
            }, 30000); // Refresh every 30 seconds
        },

        stopAutoRefresh() {
            if (this.refreshInterval) {
                clearInterval(this.refreshInterval);
                this.refreshInterval = null;
            }
        },

        refreshTable() {
            const params = new URLSearchParams(new FormData(this.$refs.filterForm));
            htmx.ajax('GET', '{% url "core:refresh_order_table" %}?' + params.toString(), {
                target: '#order-table-container',
                swap: 'innerHTML'
            });
        },

        applyFilters() {
            this.$refs.filterForm.submit();
        },

        debounceSearch(event) {
            clearTimeout(this.searchTimeout);
            this.searchTimeout = setTimeout(() => {
                this.applyFilters();
            }, 500);
        },

        clearFilters() {
            // Reset all form fields
            this.$refs.filterForm.reset();
            this.applyFilters();
        },

        toggleOrderSelection(orderId) {
            const index = this.selectedOrders.indexOf(orderId);
            if (index > -1) {
                this.selectedOrders.splice(index, 1);
            } else {
                this.selectedOrders.push(orderId);
            }
        },

        selectAllOrders() {
            const checkboxes = document.querySelectorAll('input[name="order_checkbox"]');
            this.selectedOrders = [];
            checkboxes.forEach(checkbox => {
                checkbox.checked = true;
                this.selectedOrders.push(parseInt(checkbox.value));
            });
        },

        clearSelection() {
            this.selectedOrders = [];
            const checkboxes = document.querySelectorAll('input[name="order_checkbox"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
        },

        bulkOperation(operation) {
            if (this.selectedOrders.length === 0) {
                this.showToast('No orders selected', 'error');
                return;
            }

            const formData = new FormData();
            formData.append('operation', operation);
            this.selectedOrders.forEach(orderId => {
                formData.append('order_ids', orderId);
            });

            htmx.ajax('POST', '{% url "core:bulk_order_operations" %}', {
                values: formData,
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                }
            }).then(() => {
                this.clearSelection();
                this.refreshTable();
            });
        },

        showOrderDetail(orderId) {
            htmx.ajax('GET', `/dealer/orders/detail/${orderId}/`, {
                target: '#order-detail-modal',
                swap: 'innerHTML'
            });
        },

        showToast(message, type = 'success') {
            // Create toast notification
            const toast = document.createElement('div');
            toast.className = `fixed top-4 right-4 px-6 py-3 rounded-md text-white z-50 ${
                type === 'success' ? 'bg-green-500' : 'bg-red-500'
            }`;
            toast.textContent = message;
            document.body.appendChild(toast);

            setTimeout(() => {
                toast.remove();
            }, 3000);
        }
    }
}

// Handle HTMX responses
document.addEventListener('htmx:responseError', function(event) {
    const response = JSON.parse(event.detail.xhr.response);
    Alpine.store('orderManagement').showToast(response.message || 'An error occurred', 'error');
});

document.addEventListener('htmx:afterRequest', function(event) {
    if (event.detail.xhr.status === 200 && event.detail.xhr.responseText.includes('success')) {
        try {
            const response = JSON.parse(event.detail.xhr.response);
            if (response.success && response.message) {
                Alpine.store('orderManagement').showToast(response.message, 'success');
            }
        } catch (e) {
            // Response is not JSON, likely HTML
        }
    }
});
</script>
{% endblock %}