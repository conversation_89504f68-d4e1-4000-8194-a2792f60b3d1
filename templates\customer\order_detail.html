{% extends 'base.html' %}

{% block title %}Order #{{ order.id }} - Prycegas Station{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Order #{{ order.id }}</h1>
                    <p class="mt-1 text-sm text-gray-600">Placed on {{ order.order_date|date:"F d, Y \a\t g:i A" }}</p>
                </div>
                <div class="flex space-x-3">
                    <a href="{% url 'core:order_history' %}" 
                       class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-prycegas-orange">
                        ← Back to Orders
                    </a>
                    <a href="{% url 'core:customer_dashboard' %}" 
                       class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-prycegas-orange">
                        Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Order Status Tracking -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h2 class="text-lg font-medium text-gray-900 mb-4">Order Status</h2>
            
            <!-- Status Progress Bar -->
            <div class="mb-6">
                <div class="flex items-center justify-between text-sm text-gray-600 mb-2">
                    <span>Order Progress</span>
                    <span>{{ progress_percentage }}%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-prycegas-orange h-2 rounded-full transition-all duration-300" 
                         style="width: {{ progress_percentage }}%"></div>
                </div>
            </div>

            <!-- Status Timeline -->
            <div class="flow-root">
                <ul class="-mb-8">
                    <!-- Pending Status -->
                    <li>
                        <div class="relative pb-8">
                            <span class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200" aria-hidden="true"></span>
                            <div class="relative flex space-x-3">
                                <div>
                                    {% if order.status == 'pending' or order.status == 'out_for_delivery' or order.status == 'delivered' %}
                                        <span class="h-8 w-8 rounded-full bg-green-500 flex items-center justify-center ring-8 ring-white">
                                            <svg class="h-5 w-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                                            </svg>
                                        </span>
                                    {% else %}
                                        <span class="h-8 w-8 rounded-full bg-gray-400 flex items-center justify-center ring-8 ring-white">
                                            <svg class="h-5 w-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                            </svg>
                                        </span>
                                    {% endif %}
                                </div>
                                <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                                    <div>
                                        <p class="text-sm text-gray-500">Order placed and confirmed</p>
                                    </div>
                                    <div class="text-right text-sm whitespace-nowrap text-gray-500">
                                        {{ order.order_date|date:"M d, g:i A" }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </li>

                    <!-- Out for Delivery Status -->
                    <li>
                        <div class="relative pb-8">
                            <span class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200" aria-hidden="true"></span>
                            <div class="relative flex space-x-3">
                                <div>
                                    {% if order.status == 'out_for_delivery' or order.status == 'delivered' %}
                                        <span class="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center ring-8 ring-white">
                                            <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                            </svg>
                                        </span>
                                    {% elif order.status == 'pending' %}
                                        <span class="h-8 w-8 rounded-full bg-yellow-500 flex items-center justify-center ring-8 ring-white">
                                            <svg class="h-5 w-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"/>
                                            </svg>
                                        </span>
                                    {% else %}
                                        <span class="h-8 w-8 rounded-full bg-gray-400 flex items-center justify-center ring-8 ring-white">
                                            <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                            </svg>
                                        </span>
                                    {% endif %}
                                </div>
                                <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                                    <div>
                                        <p class="text-sm text-gray-500">
                                            {% if order.delivery_type == 'pickup' %}
                                                Ready for pickup
                                            {% else %}
                                                Out for delivery
                                            {% endif %}
                                        </p>
                                    </div>
                                    <div class="text-right text-sm whitespace-nowrap text-gray-500">
                                        {% if order.status == 'out_for_delivery' or order.status == 'delivered' %}
                                            {% if order.delivery_date %}
                                                {{ order.delivery_date|date:"M d, g:i A" }}
                                            {% else %}
                                                In progress
                                            {% endif %}
                                        {% else %}
                                            Pending
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </li>

                    <!-- Delivered Status -->
                    <li>
                        <div class="relative">
                            <div class="relative flex space-x-3">
                                <div>
                                    {% if order.status == 'delivered' %}
                                        <span class="h-8 w-8 rounded-full bg-green-500 flex items-center justify-center ring-8 ring-white">
                                            <svg class="h-5 w-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                                            </svg>
                                        </span>
                                    {% elif order.status == 'cancelled' %}
                                        <span class="h-8 w-8 rounded-full bg-red-500 flex items-center justify-center ring-8 ring-white">
                                            <svg class="h-5 w-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
                                            </svg>
                                        </span>
                                    {% else %}
                                        <span class="h-8 w-8 rounded-full bg-gray-400 flex items-center justify-center ring-8 ring-white">
                                            <svg class="h-5 w-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                                            </svg>
                                        </span>
                                    {% endif %}
                                </div>
                                <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                                    <div>
                                        <p class="text-sm text-gray-500">
                                            {% if order.status == 'delivered' %}
                                                Order completed
                                            {% elif order.status == 'cancelled' %}
                                                Order cancelled
                                            {% else %}
                                                Awaiting completion
                                            {% endif %}
                                        </p>
                                    </div>
                                    <div class="text-right text-sm whitespace-nowrap text-gray-500">
                                        {% if order.delivery_date %}
                                            {{ order.delivery_date|date:"M d, g:i A" }}
                                        {% elif order.status == 'cancelled' %}
                                            Cancelled
                                        {% else %}
                                            Pending
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </li>
                </ul>
            </div>

            <!-- Current Status Badge -->
            <div class="mt-6 flex items-center justify-center">
                {% if order.status == 'pending' %}
                    <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                        <svg class="-ml-1 mr-2 h-4 w-4 text-yellow-400" fill="currentColor" viewBox="0 0 8 8">
                            <circle cx="4" cy="4" r="3"/>
                        </svg>
                        Order Pending
                    </span>
                {% elif order.status == 'out_for_delivery' %}
                    <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                        <svg class="-ml-1 mr-2 h-4 w-4 text-blue-400" fill="currentColor" viewBox="0 0 8 8">
                            <circle cx="4" cy="4" r="3"/>
                        </svg>
                        {% if order.delivery_type == 'pickup' %}Ready for Pickup{% else %}Out for Delivery{% endif %}
                    </span>
                {% elif order.status == 'delivered' %}
                    <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-green-100 text-green-800">
                        <svg class="-ml-1 mr-2 h-4 w-4 text-green-400" fill="currentColor" viewBox="0 0 8 8">
                            <circle cx="4" cy="4" r="3"/>
                        </svg>
                        Order Completed
                    </span>
                {% elif order.status == 'cancelled' %}
                    <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-red-100 text-red-800">
                        <svg class="-ml-1 mr-2 h-4 w-4 text-red-400" fill="currentColor" viewBox="0 0 8 8">
                            <circle cx="4" cy="4" r="3"/>
                        </svg>
                        Order Cancelled
                    </span>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Order Details -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Product Information -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Product Details</h3>
                <div class="space-y-4">
                    <div class="flex flex-col sm:flex-row sm:justify-between">
                        <span class="text-sm font-medium text-gray-500">Product:</span>
                        <span class="text-sm text-gray-900 mt-1 sm:mt-0">{{ order.product.name }}</span>
                    </div>
                    <div class="flex flex-col sm:flex-row sm:justify-between">
                        <span class="text-sm font-medium text-gray-500">Size:</span>
                        <span class="text-sm text-gray-900 mt-1 sm:mt-0">{{ order.product.size }}</span>
                    </div>
                    <div class="flex flex-col sm:flex-row sm:justify-between">
                        <span class="text-sm font-medium text-gray-500">Quantity:</span>
                        <span class="text-sm text-gray-900 mt-1 sm:mt-0">{{ order.quantity }}</span>
                    </div>
                    <div class="flex flex-col sm:flex-row sm:justify-between">
                        <span class="text-sm font-medium text-gray-500">Unit Price:</span>
                        <span class="text-sm text-gray-900 mt-1 sm:mt-0">₱{{ order.product.price|floatformat:2 }}</span>
                    </div>
                    <div class="border-t pt-4">
                        <div class="flex flex-col sm:flex-row sm:justify-between">
                            <span class="text-base font-medium text-gray-900">Total Amount:</span>
                            <span class="text-base font-bold text-prycegas-orange mt-1 sm:mt-0">₱{{ order.total_amount|floatformat:2 }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Delivery Information -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Delivery Information</h3>
                <div class="space-y-4">
                    <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center">
                        <span class="text-sm font-medium text-gray-500">Delivery Type:</span>
                        <span class="text-sm text-gray-900 mt-1 sm:mt-0">
                            {% if order.delivery_type == 'pickup' %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    Pickup
                                </span>
                            {% else %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    Delivery
                                </span>
                            {% endif %}
                        </span>
                    </div>
                    {% if order.delivery_type == 'delivery' %}
                    <div>
                        <span class="text-sm font-medium text-gray-500 block mb-1">Delivery Address:</span>
                        <p class="text-sm text-gray-900 break-words">{{ order.delivery_address }}</p>
                    </div>
                    {% endif %}
                    <div class="flex flex-col sm:flex-row sm:justify-between">
                        <span class="text-sm font-medium text-gray-500">Order Date:</span>
                        <span class="text-sm text-gray-900 mt-1 sm:mt-0">{{ order.order_date|date:"M d, Y g:i A" }}</span>
                    </div>
                    {% if order.delivery_date %}
                    <div class="flex flex-col sm:flex-row sm:justify-between">
                        <span class="text-sm font-medium text-gray-500">
                            {% if order.delivery_type == 'pickup' %}Pickup Date:{% else %}Delivery Date:{% endif %}
                        </span>
                        <span class="text-sm text-gray-900 mt-1 sm:mt-0">{{ order.delivery_date|date:"M d, Y g:i A" }}</span>
                    </div>
                    {% endif %}
                    {% if order.notes %}
                    <div>
                        <span class="text-sm font-medium text-gray-500 block mb-1">Notes:</span>
                        <p class="text-sm text-gray-900 break-words">{{ order.notes }}</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Auto-refresh for real-time updates -->
    <div hx-get="{% url 'core:order_detail' order.id %}" 
         hx-trigger="every 30s" 
         hx-select="#order-status-section"
         hx-target="#order-status-section"
         hx-swap="outerHTML">
    </div>
</div>

<script>
// Auto-refresh order status every 30 seconds for real-time updates
document.addEventListener('DOMContentLoaded', function() {
    // Only refresh if order is not in final state
    {% if order.status != 'delivered' and order.status != 'cancelled' %}
    setInterval(function() {
        // Trigger HTMX refresh for order status
        htmx.trigger(document.body, 'refreshOrderStatus');
    }, 30000); // 30 seconds
    {% endif %}
});
</script>
{% endblock %}