from django.contrib import admin
from .models import CustomerProfile, LPGProduct, Order, DeliveryLog


@admin.register(CustomerProfile)
class CustomerProfileAdmin(admin.ModelAdmin):
    list_display = ('user', 'phone_number', 'created_at')
    list_filter = ('created_at',)
    search_fields = ('user__username', 'user__email', 'phone_number')
    readonly_fields = ('created_at', 'updated_at')


@admin.register(LPGProduct)
class LPGProductAdmin(admin.ModelAdmin):
    list_display = ('name', 'size', 'price', 'current_stock', 'minimum_stock', 'is_low_stock', 'is_active')
    list_filter = ('is_active', 'created_at')
    search_fields = ('name', 'size')
    readonly_fields = ('created_at', 'updated_at')
    list_editable = ('price', 'current_stock', 'minimum_stock', 'is_active')
    
    def is_low_stock(self, obj):
        return obj.is_low_stock
    is_low_stock.boolean = True
    is_low_stock.short_description = 'Low Stock'


@admin.register(Order)
class OrderAdmin(admin.ModelAdmin):
    list_display = ('id', 'customer', 'product', 'quantity', 'delivery_type', 'status', 'total_amount', 'order_date')
    list_filter = ('status', 'delivery_type', 'order_date')
    search_fields = ('customer__username', 'product__name')
    readonly_fields = ('total_amount', 'order_date', 'created_at', 'updated_at')
    list_editable = ('status',)
    date_hierarchy = 'order_date'


@admin.register(DeliveryLog)
class DeliveryLogAdmin(admin.ModelAdmin):
    list_display = ('product', 'quantity_received', 'supplier', 'delivery_date', 'total_cost', 'logged_by')
    list_filter = ('delivery_date', 'supplier')
    search_fields = ('product__name', 'supplier')
    readonly_fields = ('total_cost', 'created_at')
    date_hierarchy = 'delivery_date'
