<!-- Sidebar component -->
<div class="flex flex-col h-full">
    <!-- Logo/Brand -->
    <div class="flex items-center justify-center h-16 px-4 bg-prycegas-black border-b border-prycegas-gray">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <svg class="h-8 w-8 text-prycegas-orange" fill="currentColor" viewBox="0 0 24 24">
                    <path
                        d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
                </svg>
            </div>
            <div class="ml-3">
                <h1 class="text-lg font-bold text-white">Prycegas</h1>
                <p class="text-xs text-gray-300">Station</p>
            </div>
        </div>
    </div>

    <!-- Navigation -->
    <nav class="flex-1 px-2 py-4 bg-prycegas-black custom-scrollbar overflow-y-auto">
        <div class="space-y-1">
            {% if user.is_authenticated %}
            {% if user.is_staff %}
            <!-- Dealer/Admin Navigation -->
            <div class="mb-6">
                <h3 class="px-3 text-xs font-semibold text-gray-400 uppercase tracking-wider">
                    Dashboard
                </h3>
                <div class="mt-2 space-y-1">
                    <a href="{% url 'core:dealer_dashboard' %}" up-follow
                        class="group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-300 hover:bg-prycegas-gray hover:text-white transition-colors duration-150">
                        <svg class="mr-3 h-5 w-5 text-gray-400 group-hover:text-prycegas-orange" fill="none"
                            viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z" />
                        </svg>
                        Overview
                    </a>
                </div>
            </div>

            <div class="mb-6">
                <h3 class="px-3 text-xs font-semibold text-gray-400 uppercase tracking-wider">
                    Orders
                </h3>
                <div class="mt-2 space-y-1">
                    <a href="{% url 'core:order_management' %}" up-follow
                        class="group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-300 hover:bg-prycegas-gray hover:text-white transition-colors duration-150">
                        <svg class="mr-3 h-5 w-5 text-gray-400 group-hover:text-prycegas-orange" fill="none"
                            viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                        </svg>
                        Manage Orders
                    </a>
                </div>
            </div>

            <div class="mb-6">
                <h3 class="px-3 text-xs font-semibold text-gray-400 uppercase tracking-wider">
                    Inventory
                </h3>
                <div class="mt-2 space-y-1">
                    <a href="{% url 'core:inventory_management' %}" up-follow
                        class="group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-300 hover:bg-prycegas-gray hover:text-white transition-colors duration-150">
                        <svg class="mr-3 h-5 w-5 text-gray-400 group-hover:text-prycegas-orange" fill="none"
                            viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                        </svg>
                        Stock Management
                    </a>
                    <a href="{% url 'core:inventory_management' %}" up-follow
                        class="group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-300 hover:bg-prycegas-gray hover:text-white transition-colors duration-150">
                        <svg class="mr-3 h-5 w-5 text-gray-400 group-hover:text-prycegas-orange" fill="none"
                            viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
                        </svg>
                        Delivery Log
                    </a>
                </div>
            </div>

            <div class="mb-6">
                <h3 class="px-3 text-xs font-semibold text-gray-400 uppercase tracking-wider">
                    Reports
                </h3>
                <div class="mt-2 space-y-1">
                    <a href="{% url 'core:reports_dashboard' %}" up-follow
                        class="group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-300 hover:bg-prycegas-gray hover:text-white transition-colors duration-150">
                        <svg class="mr-3 h-5 w-5 text-gray-400 group-hover:text-prycegas-orange" fill="none"
                            viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                        </svg>
                        Reports Dashboard
                    </a>
                    <a href="{% url 'core:sales_report' %}" up-follow
                        class="group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-300 hover:bg-prycegas-gray hover:text-white transition-colors duration-150">
                        <svg class="mr-3 h-5 w-5 text-gray-400 group-hover:text-prycegas-orange" fill="none"
                            viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                        </svg>
                        Sales Reports
                    </a>
                    <a href="{% url 'core:stock_report' %}" up-follow
                        class="group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-300 hover:bg-prycegas-gray hover:text-white transition-colors duration-150">
                        <svg class="mr-3 h-5 w-5 text-gray-400 group-hover:text-prycegas-orange" fill="none"
                            viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        Stock Reports
                    </a>
                </div>
            </div>
            {% else %}
            <!-- Customer Navigation -->
            <div class="mb-6">
                <h3 class="px-3 text-xs font-semibold text-gray-400 uppercase tracking-wider">
                    My Account
                </h3>
                <div class="mt-2 space-y-1">
                    <a href="{% url 'core:customer_dashboard' %}" up-follow
                        class="group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-300 hover:bg-prycegas-gray hover:text-white transition-colors duration-150">
                        <svg class="mr-3 h-5 w-5 text-gray-400 group-hover:text-prycegas-orange" fill="none"
                            viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z" />
                        </svg>
                        Dashboard
                    </a>
                    <a href="{% url 'core:profile' %}" up-follow
                        class="group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-300 hover:bg-prycegas-gray hover:text-white transition-colors duration-150">
                        <svg class="mr-3 h-5 w-5 text-gray-400 group-hover:text-prycegas-orange" fill="none"
                            viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                        Profile
                    </a>
                </div>
            </div>

            <div class="mb-6">
                <h3 class="px-3 text-xs font-semibold text-gray-400 uppercase tracking-wider">
                    Orders
                </h3>
                <div class="mt-2 space-y-1">
                    <a href="{% url 'core:place_order' %}" up-follow
                        class="group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-300 hover:bg-prycegas-gray hover:text-white transition-colors duration-150">
                        <svg class="mr-3 h-5 w-5 text-gray-400 group-hover:text-prycegas-orange" fill="none"
                            viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                        Place Order
                    </a>
                    <a href="{% url 'core:order_history' %}" up-follow
                        class="group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-300 hover:bg-prycegas-gray hover:text-white transition-colors duration-150">
                        <svg class="mr-3 h-5 w-5 text-gray-400 group-hover:text-prycegas-orange" fill="none"
                            viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                        </svg>
                        Order History
                    </a>
                </div>
            </div>
            {% endif %}
            {% else %}
            <!-- Guest Navigation -->
            <div class="mb-6">
                <div class="mt-2 space-y-1">
                    <a href="{% url 'core:login' %}" up-follow
                        class="group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-300 hover:bg-prycegas-gray hover:text-white transition-colors duration-150">
                        <svg class="mr-3 h-5 w-5 text-gray-400 group-hover:text-prycegas-orange" fill="none"
                            viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                        </svg>
                        Login
                    </a>
                    <a href="{% url 'core:register' %}" up-follow
                        class="group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-300 hover:bg-prycegas-gray hover:text-white transition-colors duration-150">
                        <svg class="mr-3 h-5 w-5 text-gray-400 group-hover:text-prycegas-orange" fill="none"
                            viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                        </svg>
                        Register
                    </a>
                </div>
            </div>
            {% endif %}
        </div>
    </nav>

    <!-- User info and logout -->
    {% if user.is_authenticated %}
    <div class="flex-shrink-0 bg-prycegas-gray border-t border-prycegas-gray-light">
        <div class="flex items-center px-4 py-4">
            <div class="flex-shrink-0">
                <div class="h-8 w-8 rounded-full bg-prycegas-orange flex items-center justify-center">
                    <span class="text-sm font-medium text-white">
                        {{ user.first_name|first|default:user.username|first|upper }}
                    </span>
                </div>
            </div>
            <div class="ml-3 flex-1 min-w-0">
                <p class="text-sm font-medium text-white truncate">
                    {{ user.get_full_name|default:user.username }}
                </p>
                <p class="text-xs text-gray-400 truncate">
                    {% if user.is_staff %}Dealer{% else %}Customer{% endif %}
                </p>
            </div>
            <div class="ml-3 flex-shrink-0">
                <form method="post" action="{% url 'core:logout' %}" class="inline">
                    {% csrf_token %}
                    <button type="submit" class="text-gray-400 hover:text-white transition-colors duration-150"
                        title="Logout">
                        <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                        </svg>
                    </button>
                </form>
            </div>
        </div>
    </div>
    {% endif %}
</div>