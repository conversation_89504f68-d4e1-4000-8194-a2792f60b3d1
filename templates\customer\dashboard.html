{% extends 'base.html' %}

{% block title %}Customer Dashboard - Prycegas Station{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Welcome Header -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Welcome, {{ user.first_name|default:user.username }}!</h1>
                    <p class="mt-1 text-sm text-gray-600">Manage your LPG orders and track deliveries</p>
                </div>
                <div class="flex space-x-3">
                    <a href="{% url 'core:place_order' %}" 
                       class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-prycegas-orange hover:bg-prycegas-orange-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-prycegas-orange">
                        <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                        </svg>
                        Place Order
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Dashboard Orders Section with Real-time Updates -->
    <div id="dashboard-orders" 
         hx-get="{% url 'core:refresh_dashboard_orders' %}"
         hx-trigger="every 30s"
         hx-swap="innerHTML">
        {% include 'customer/dashboard_orders_partial.html' %}
    </div>

    <!-- Recent Orders -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-lg font-medium text-gray-900">Recent Orders</h2>
                <div class="flex items-center space-x-3">
                    <button onclick="refreshDashboard()" 
                            class="text-sm text-gray-500 hover:text-gray-700 font-medium">
                        <svg class="inline h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                        </svg>
                        Refresh
                    </button>
                    <a href="{% url 'core:order_history' %}" 
                       class="text-sm text-prycegas-orange hover:text-prycegas-orange-dark font-medium">
                        View all orders →
                    </a>
                </div>
            </div>
            
            {% if recent_orders %}
                {% include 'customer/dashboard_orders_partial.html' %}
            {% else %}
                <div class="text-center py-8">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No orders yet</h3>
                    <p class="mt-1 text-sm text-gray-500">Get started by placing your first order.</p>
                    <div class="mt-6">
                        <a href="{% url 'core:place_order' %}" 
                           class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-prycegas-orange hover:bg-prycegas-orange-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-prycegas-orange">
                            <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                            </svg>
                            Place Your First Order
                        </a>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
function refreshDashboard() {
    htmx.trigger('#dashboard-orders', 'htmx:refresh');
}

// Auto-refresh dashboard every 30 seconds for real-time updates
document.addEventListener('DOMContentLoaded', function() {
    // Add loading indicator for HTMX requests
    document.body.addEventListener('htmx:beforeRequest', function(evt) {
        if (evt.target.id === 'dashboard-orders') {
            evt.target.style.opacity = '0.7';
        }
    });
    
    document.body.addEventListener('htmx:afterRequest', function(evt) {
        if (evt.target.id === 'dashboard-orders') {
            evt.target.style.opacity = '1';
        }
    });
});
</script>
{% endblock %}